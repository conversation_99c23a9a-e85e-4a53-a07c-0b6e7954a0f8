import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { DoctorNoteForm } from '../../../components/doctor/doctor-note-form'
import { DoctorNote } from '../../../lib/types'

// Mock fetch
global.fetch = jest.fn()
const mockFetch = fetch as jest.MockedFunction<typeof fetch>

// Mock toast
jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn()
  }
}))

// Mock data
const mockNote: DoctorNote = {
  id: 'note-1',
  patientId: 'patient-1',
  doctorId: 'doctor-1',
  title: 'Test Note',
  content: 'This is test note content',
  noteType: 'general',
  isPrivate: false,
  tags: ['test', 'general'],
  version: 1,
  isDeleted: false,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  appointmentId: null
}

describe('DoctorNoteForm', () => {
  const defaultProps = {
    doctorId: 'doctor-1',
    patientId: 'patient-1',
    onSave: jest.fn(),
    onCancel: jest.fn()
  }

  beforeEach(() => {
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        success: true,
        data: mockNote
      })
    } as Response)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('should render create form correctly', () => {
    render(<DoctorNoteForm {...defaultProps} />)

    expect(screen.getByText('Create New Note')).toBeInTheDocument()
    expect(screen.getByLabelText('Title')).toBeInTheDocument()
    expect(screen.getByLabelText('Content')).toBeInTheDocument()
    expect(screen.getByLabelText('Note Type')).toBeInTheDocument()
    expect(screen.getByLabelText('Tags')).toBeInTheDocument()
    expect(screen.getByLabelText('Private note (only visible to you)')).toBeInTheDocument()
    expect(screen.getByText('Create Note')).toBeInTheDocument()
    expect(screen.getByText('Cancel')).toBeInTheDocument()
  })

  it('should render edit form with existing note data', () => {
    render(<DoctorNoteForm {...defaultProps} note={mockNote} />)

    expect(screen.getByText('Edit Note')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Test Note')).toBeInTheDocument()
    expect(screen.getByDisplayValue('This is test note content')).toBeInTheDocument()
    expect(screen.getByDisplayValue('general')).toBeInTheDocument()
    expect(screen.getByText('test')).toBeInTheDocument()
    expect(screen.getByText('general')).toBeInTheDocument()
    expect(screen.getByText('Update Note')).toBeInTheDocument()
  })

  it('should handle form submission for creating new note', async () => {
    render(<DoctorNoteForm {...defaultProps} />)

    // Fill out the form
    fireEvent.change(screen.getByLabelText('Title'), {
      target: { value: 'New Test Note' }
    })
    fireEvent.change(screen.getByLabelText('Content'), {
      target: { value: 'New test note content' }
    })
    fireEvent.change(screen.getByLabelText('Note Type'), {
      target: { value: 'consultation' }
    })

    // Submit the form
    fireEvent.click(screen.getByText('Create Note'))

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/doctor-notes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          patientId: 'patient-1',
          doctorId: 'doctor-1',
          title: 'New Test Note',
          content: 'New test note content',
          noteType: 'consultation',
          isPrivate: false,
          tags: []
        })
      })
    })

    const { toast } = require('sonner')
    expect(toast.success).toHaveBeenCalledWith('Note created successfully')
    expect(defaultProps.onSave).toHaveBeenCalledWith(mockNote)
  })

  it('should handle form submission for updating existing note', async () => {
    render(<DoctorNoteForm {...defaultProps} note={mockNote} />)

    // Update the title
    fireEvent.change(screen.getByDisplayValue('Test Note'), {
      target: { value: 'Updated Test Note' }
    })

    // Submit the form
    fireEvent.click(screen.getByText('Update Note'))

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith(`/api/doctor-notes/${mockNote.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: 'Updated Test Note',
          content: 'This is test note content',
          noteType: 'general',
          isPrivate: false,
          tags: ['test', 'general'],
          doctorId: 'doctor-1'
        })
      })
    })

    const { toast } = require('sonner')
    expect(toast.success).toHaveBeenCalledWith('Note updated successfully')
    expect(defaultProps.onSave).toHaveBeenCalledWith(mockNote)
  })

  it('should validate required fields', async () => {
    render(<DoctorNoteForm {...defaultProps} />)

    // Try to submit without filling required fields
    fireEvent.click(screen.getByText('Create Note'))

    const { toast } = require('sonner')
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Title and content are required')
    })

    expect(mockFetch).not.toHaveBeenCalled()
  })

  it('should handle tag management', () => {
    render(<DoctorNoteForm {...defaultProps} />)

    const tagInput = screen.getByLabelText('Tags')
    const addButton = screen.getByText('Add')

    // Add a tag
    fireEvent.change(tagInput, { target: { value: 'new-tag' } })
    fireEvent.click(addButton)

    expect(screen.getByText('new-tag')).toBeInTheDocument()

    // Remove the tag
    const removeButton = screen.getByText('×')
    fireEvent.click(removeButton)

    expect(screen.queryByText('new-tag')).not.toBeInTheDocument()
  })

  it('should handle tag addition with Enter key', () => {
    render(<DoctorNoteForm {...defaultProps} />)

    const tagInput = screen.getByLabelText('Tags')

    // Add a tag with Enter key
    fireEvent.change(tagInput, { target: { value: 'enter-tag' } })
    fireEvent.keyPress(tagInput, { key: 'Enter', code: 'Enter' })

    expect(screen.getByText('enter-tag')).toBeInTheDocument()
  })

  it('should prevent duplicate tags', () => {
    render(<DoctorNoteForm {...defaultProps} note={mockNote} />)

    const tagInput = screen.getByLabelText('Tags')
    const addButton = screen.getByText('Add')

    // Try to add an existing tag
    fireEvent.change(tagInput, { target: { value: 'test' } })
    fireEvent.click(addButton)

    // Should still only have one 'test' tag
    const testTags = screen.getAllByText('test')
    expect(testTags).toHaveLength(1)
  })

  it('should handle privacy toggle', () => {
    render(<DoctorNoteForm {...defaultProps} />)

    const privateCheckbox = screen.getByLabelText('Private note (only visible to you)')
    
    expect(privateCheckbox).not.toBeChecked()
    
    fireEvent.click(privateCheckbox)
    
    expect(privateCheckbox).toBeChecked()
  })

  it('should handle cancel button', () => {
    render(<DoctorNoteForm {...defaultProps} />)

    fireEvent.click(screen.getByText('Cancel'))
    
    expect(defaultProps.onCancel).toHaveBeenCalled()
  })

  it('should handle API errors', async () => {
    mockFetch.mockResolvedValue({
      ok: false,
      json: async () => ({
        success: false,
        error: 'Failed to create note'
      })
    } as Response)

    render(<DoctorNoteForm {...defaultProps} />)

    // Fill out and submit the form
    fireEvent.change(screen.getByLabelText('Title'), {
      target: { value: 'Test Note' }
    })
    fireEvent.change(screen.getByLabelText('Content'), {
      target: { value: 'Test content' }
    })
    fireEvent.click(screen.getByText('Create Note'))

    const { toast } = require('sonner')
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to create note')
    })
  })

  it('should show loading state during submission', async () => {
    // Mock a delayed response
    mockFetch.mockImplementation(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({
          ok: true,
          json: async () => ({ success: true, data: mockNote })
        } as Response), 100)
      )
    )

    render(<DoctorNoteForm {...defaultProps} />)

    // Fill out and submit the form
    fireEvent.change(screen.getByLabelText('Title'), {
      target: { value: 'Test Note' }
    })
    fireEvent.change(screen.getByLabelText('Content'), {
      target: { value: 'Test content' }
    })
    fireEvent.click(screen.getByText('Create Note'))

    // Should show loading state
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })
})
