import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import AdminDashboard from '../../app/dashboard/admin/page'

// Mock ProtectedRoute component
jest.mock('../../components/auth', () => ({
  ProtectedRoute: ({ children, allowedRoles }: any) => (
    <div data-testid="protected-route" data-allowed-roles={allowedRoles?.join(',')}>
      {children}
    </div>
  ),
}))

// Mock UI components
jest.mock('../../components/ui', () => ({
  Card: ({ children, className, ...props }: any) => (
    <div data-testid="card" className={className} {...props}>
      {children}
    </div>
  ),
  CardContent: ({ children, className, ...props }: any) => (
    <div data-testid="card-content" className={className} {...props}>
      {children}
    </div>
  ),
  CardDescription: ({ children, className, ...props }: any) => (
    <div data-testid="card-description" className={className} {...props}>
      {children}
    </div>
  ),
  CardHeader: ({ children, className, ...props }: any) => (
    <div data-testid="card-header" className={className} {...props}>
      {children}
    </div>
  ),
  CardTitle: ({ children, className, ...props }: any) => (
    <div data-testid="card-title" className={className} {...props}>
      {children}
    </div>
  ),
  Button: ({ children, onClick, variant, className, ...props }: any) => (
    <button 
      onClick={onClick} 
      data-variant={variant} 
      className={className} 
      {...props}
    >
      {children}
    </button>
  ),
  Badge: ({ children, variant, className, ...props }: any) => (
    <span data-testid="badge" data-variant={variant} className={className} {...props}>
      {children}
    </span>
  ),
}))

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Users: ({ className }: any) => <div data-testid="users-icon" className={className} />,
  UserPlus: ({ className }: any) => <div data-testid="user-plus-icon" className={className} />,
  Calendar: ({ className }: any) => <div data-testid="calendar-icon" className={className} />,
  FileText: ({ className }: any) => <div data-testid="file-text-icon" className={className} />,
  Settings: ({ className }: any) => <div data-testid="settings-icon" className={className} />,
  Activity: ({ className }: any) => <div data-testid="activity-icon" className={className} />,
  TrendingUp: ({ className }: any) => <div data-testid="trending-up-icon" className={className} />,
  Clock: ({ className }: any) => <div data-testid="clock-icon" className={className} />,
  AlertCircle: ({ className }: any) => <div data-testid="alert-circle-icon" className={className} />,
}))

// Mock utils
jest.mock('../../lib/utils', () => ({
  formatDate: (date: Date) => date.toLocaleDateString(),
  formatDateTime: (date: Date) => date.toLocaleString(),
}))

describe('Admin Dashboard Page', () => {
  describe('Component Structure', () => {
    it('should render within ProtectedRoute with admin role', () => {
      render(<AdminDashboard />)

      const protectedRoute = screen.getByTestId('protected-route')
      expect(protectedRoute).toBeInTheDocument()
      expect(protectedRoute).toHaveAttribute('data-allowed-roles', 'admin')
    })

    it('should render main dashboard header', () => {
      render(<AdminDashboard />)

      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument()
      expect(screen.getByText('Manage your healthcare system and monitor key metrics')).toBeInTheDocument()
    })

    it('should render header action buttons', () => {
      render(<AdminDashboard />)

      expect(screen.getByText('Export Report')).toBeInTheDocument()
      expect(screen.getByText('Invite Doctor')).toBeInTheDocument()

      // Check that buttons are clickable
      const exportButton = screen.getByText('Export Report').closest('button')
      const inviteButton = screen.getByText('Invite Doctor').closest('button')
      expect(exportButton).toBeInTheDocument()
      expect(inviteButton).toBeInTheDocument()
    })
  })

  describe('Statistics Cards', () => {
    it('should render all statistics cards with correct data', () => {
      render(<AdminDashboard />)

      // Check for statistics cards with actual component data
      expect(screen.getByText('Total Patients')).toBeInTheDocument()
      expect(screen.getByText('156')).toBeInTheDocument()
      expect(screen.getByText('Active patient accounts')).toBeInTheDocument()

      expect(screen.getByText('Total Doctors')).toBeInTheDocument()
      expect(screen.getByText('12')).toBeInTheDocument()
      expect(screen.getByText('Active doctor accounts')).toBeInTheDocument()

      expect(screen.getByText('Appointments')).toBeInTheDocument()
      expect(screen.getByText('89')).toBeInTheDocument()
      expect(screen.getByText('This month')).toBeInTheDocument()

      expect(screen.getByText('Pending Invitations')).toBeInTheDocument()
      expect(screen.getByText('3')).toBeInTheDocument()
      expect(screen.getByText('Doctor invitations')).toBeInTheDocument()
    })

    it('should render statistics icons correctly', () => {
      render(<AdminDashboard />)

      // Use getAllByTestId for icons that appear multiple times
      const usersIcons = screen.getAllByTestId('users-icon')
      const userPlusIcons = screen.getAllByTestId('user-plus-icon')
      const calendarIcons = screen.getAllByTestId('calendar-icon')
      const activityIcons = screen.getAllByTestId('activity-icon')

      expect(usersIcons.length).toBeGreaterThan(0)
      expect(userPlusIcons.length).toBeGreaterThan(0)
      expect(calendarIcons.length).toBeGreaterThan(0)
      expect(activityIcons.length).toBeGreaterThan(0)
    })
  })

  describe('Recent Activity Section', () => {
    it('should render recent activity card with activities', () => {
      render(<AdminDashboard />)

      expect(screen.getByText('Recent Activity')).toBeInTheDocument()
      expect(screen.getByText('Latest system activities and updates')).toBeInTheDocument()

      // Check for actual activity items from mockStats
      expect(screen.getByText('New patient John Doe registered')).toBeInTheDocument()
      expect(screen.getByText('Dr. Sarah Wilson invited')).toBeInTheDocument()
      expect(screen.getByText('Appointment scheduled for Patient #123')).toBeInTheDocument()
      expect(screen.getByText('Dr. Michael Brown accepted invitation')).toBeInTheDocument()
    })

    it('should render activity timestamps', () => {
      render(<AdminDashboard />)

      // Check for actual timestamps from mockStats
      expect(screen.getByText('2 hours ago')).toBeInTheDocument()
      expect(screen.getByText('4 hours ago')).toBeInTheDocument()
      expect(screen.getByText('6 hours ago')).toBeInTheDocument()
      expect(screen.getByText('1 day ago')).toBeInTheDocument()
    })

  })

  describe('Recent Users Section', () => {
    it('should render recent users card', () => {
      render(<AdminDashboard />)

      expect(screen.getByText('Recent Users')).toBeInTheDocument()
      expect(screen.getByText('Newly registered users and invitations')).toBeInTheDocument()
    })

    it('should render user list with correct data', () => {
      render(<AdminDashboard />)

      // Check for actual users from mockRecentUsers
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()

      expect(screen.getByText('Dr. Sarah Wilson')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()

      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()

      expect(screen.getByText('Dr. Michael Brown')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    })

    it('should render user badges for roles and status', () => {
      render(<AdminDashboard />)

      // Check for role and status badges
      const patientBadges = screen.getAllByText('patient')
      const doctorBadges = screen.getAllByText('doctor')
      const activeBadges = screen.getAllByText('active')
      const pendingBadges = screen.getAllByText('pending')

      expect(patientBadges.length).toBeGreaterThan(0)
      expect(doctorBadges.length).toBeGreaterThan(0)
      expect(activeBadges.length).toBeGreaterThan(0)
      expect(pendingBadges.length).toBeGreaterThan(0)
    })
  })

  describe('Quick Actions Section', () => {
    it('should render quick actions card', () => {
      render(<AdminDashboard />)

      expect(screen.getByText('Quick Actions')).toBeInTheDocument()
      expect(screen.getByText('Common administrative tasks')).toBeInTheDocument()
    })

    it('should render all quick action buttons', () => {
      render(<AdminDashboard />)

      // Check for actual quick action buttons from component
      expect(screen.getByText('Invite New Doctor')).toBeInTheDocument()
      expect(screen.getByText('Manage Users')).toBeInTheDocument()
      expect(screen.getByText('System Reports')).toBeInTheDocument()
    })

    it('should render quick action icons', () => {
      render(<AdminDashboard />)

      // Icons should be present (multiple instances due to header and quick actions)
      const userPlusIcons = screen.getAllByTestId('user-plus-icon')
      const usersIcons = screen.getAllByTestId('users-icon')
      const fileTextIcons = screen.getAllByTestId('file-text-icon')

      expect(userPlusIcons.length).toBeGreaterThan(0)
      expect(usersIcons.length).toBeGreaterThan(0)
      expect(fileTextIcons.length).toBeGreaterThan(0)
    })

    it('should handle quick action button clicks', () => {
      render(<AdminDashboard />)

      // Test clicking on quick action buttons
      const inviteButton = screen.getByText('Invite New Doctor')
      const manageUsersButton = screen.getByText('Manage Users')
      const reportsButton = screen.getByText('System Reports')

      expect(inviteButton.closest('button')).toBeInTheDocument()
      expect(manageUsersButton.closest('button')).toBeInTheDocument()
      expect(reportsButton.closest('button')).toBeInTheDocument()

      // Should be clickable
      fireEvent.click(inviteButton)
      fireEvent.click(manageUsersButton)
      fireEvent.click(reportsButton)
      // In a real app, this would trigger navigation or modal
    })
  })

  describe('Responsive Layout', () => {
    it('should apply correct CSS classes for responsive design', () => {
      render(<AdminDashboard />)

      // Check for responsive grid classes - need to find the actual grid container
      const statsGrid = screen.getByText('Total Patients').closest('[data-testid="card"]')?.parentElement
      expect(statsGrid).toHaveClass('grid', 'gap-4', 'md:grid-cols-2', 'lg:grid-cols-4')

      const contentGrid = screen.getByText('Recent Activity').closest('[data-testid="card"]')?.parentElement
      expect(contentGrid).toHaveClass('grid', 'gap-6', 'md:grid-cols-2')

      // For quick actions, find the grid container that contains the buttons
      const quickActionsContainer = screen.getByText('Invite New Doctor').closest('button')?.parentElement
      expect(quickActionsContainer).toHaveClass('grid', 'gap-4', 'md:grid-cols-3')
    })
  })

  describe('Data Display', () => {
    it('should display formatted data correctly', () => {
      render(<AdminDashboard />)

      // Numbers should be displayed as strings (actual component data)
      expect(screen.getByText('156')).toBeInTheDocument() // Total Patients
      expect(screen.getByText('12')).toBeInTheDocument()  // Total Doctors
      expect(screen.getByText('89')).toBeInTheDocument()  // Appointments
      expect(screen.getByText('3')).toBeInTheDocument()   // Pending Invitations

      // Trend percentages
      expect(screen.getByText('+12% from last month')).toBeInTheDocument()
      expect(screen.getByText('+8% from last month')).toBeInTheDocument()
      expect(screen.getByText('+15% from last month')).toBeInTheDocument()
    })

    it('should display user initials correctly', () => {
      render(<AdminDashboard />)

      // User initials should be displayed in avatar circles - use getAllByText for multiple instances
      const jInitials = screen.getAllByText('J') // John Doe and Jane Smith
      const dInitials = screen.getAllByText('D') // Dr. Sarah Wilson and Dr. Michael Brown

      expect(jInitials.length).toBeGreaterThan(0)
      expect(dInitials.length).toBeGreaterThan(0)
    })
  })
})
