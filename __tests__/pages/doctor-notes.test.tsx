import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import DoctorNotesPage from '../../app/dashboard/doctor/notes/page'
import { useAuth } from '../../lib/contexts/auth.context'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn()
}))

// Mock auth context
jest.mock('../../lib/contexts/auth.context', () => ({
  useAuth: jest.fn()
}))

// Mock components
jest.mock('../../components/doctor/doctor-notes-list', () => ({
  DoctorNotesList: ({ onNoteSelect, onCreateNote }: any) => (
    <div data-testid="doctor-notes-list">
      <button onClick={() => onNoteSelect({ id: 'note-1', title: 'Test Note' })}>
        Select Note
      </button>
      <button onClick={onCreateNote}>Create Note</button>
    </div>
  )
}))

jest.mock('../../components/doctor/doctor-note-form', () => ({
  DoctorNoteForm: ({ onSave, onCancel }: any) => (
    <div data-testid="doctor-note-form">
      <button onClick={() => onSave({ id: 'note-1', title: 'Test Note' })}>
        Save Note
      </button>
      <button onClick={onCancel}>Cancel</button>
    </div>
  )
}))

jest.mock('../../components/doctor/doctor-note-view', () => ({
  DoctorNoteView: ({ note, onEdit, onDelete }: any) => (
    <div data-testid="doctor-note-view">
      <h2>{note.title}</h2>
      <button onClick={onEdit}>Edit Note</button>
      <button onClick={onDelete}>Delete Note</button>
    </div>
  )
}))

// Mock fetch
global.fetch = jest.fn()
const mockFetch = fetch as jest.MockedFunction<typeof fetch>

// Mock toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}))

describe('DoctorNotesPage', () => {
  const mockPush = jest.fn()
  const mockUser = {
    uid: 'doctor-123',
    email: '<EMAIL>',
    role: 'doctor' as const
  }

  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush
    });
    
    (useAuth as jest.Mock).mockReturnValue({
      user: mockUser,
      loading: false
    })

    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ success: true })
    } as Response)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('should render doctor notes page', () => {
    render(<DoctorNotesPage />)

    expect(screen.getByText('My Notes')).toBeInTheDocument()
    expect(screen.getByTestId('doctor-notes-list')).toBeInTheDocument()
  })

  it('should redirect if user is not authenticated', () => {
    (useAuth as jest.Mock).mockReturnValue({
      user: null,
      loading: false
    })

    render(<DoctorNotesPage />)

    expect(mockPush).toHaveBeenCalledWith('/login')
  })

  it('should redirect if user is not a doctor', () => {
    (useAuth as jest.Mock).mockReturnValue({
      user: { ...mockUser, role: 'patient' },
      loading: false
    })

    render(<DoctorNotesPage />)

    expect(mockPush).toHaveBeenCalledWith('/dashboard')
  })

  it('should show loading state', () => {
    (useAuth as jest.Mock).mockReturnValue({
      user: null,
      loading: true
    })

    render(<DoctorNotesPage />)

    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  it('should handle note selection', () => {
    render(<DoctorNotesPage />)

    fireEvent.click(screen.getByText('Select Note'))

    expect(screen.getByTestId('doctor-note-view')).toBeInTheDocument()
    expect(screen.getByText('Test Note')).toBeInTheDocument()
  })

  it('should handle create note', () => {
    render(<DoctorNotesPage />)

    fireEvent.click(screen.getByText('Create Note'))

    expect(screen.getByTestId('doctor-note-form')).toBeInTheDocument()
  })

  it('should handle edit note', () => {
    render(<DoctorNotesPage />)

    // First select a note
    fireEvent.click(screen.getByText('Select Note'))
    
    // Then edit it
    fireEvent.click(screen.getByText('Edit Note'))

    expect(screen.getByTestId('doctor-note-form')).toBeInTheDocument()
  })

  it('should handle delete note', async () => {
    render(<DoctorNotesPage />)

    // First select a note
    fireEvent.click(screen.getByText('Select Note'))
    
    // Mock window.confirm
    window.confirm = jest.fn().mockReturnValue(true)
    
    // Then delete it
    fireEvent.click(screen.getByText('Delete Note'))

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith(
        '/api/doctor-notes/note-1?doctorId=doctor-123',
        { method: 'DELETE' }
      )
    })

    const { toast } = require('sonner')
    expect(toast.success).toHaveBeenCalledWith('Note deleted successfully')
  })

  it('should handle save note', async () => {
    render(<DoctorNotesPage />)

    fireEvent.click(screen.getByText('Create Note'))
    fireEvent.click(screen.getByText('Save Note'))

    // Should close the form and refresh the list
    expect(screen.queryByTestId('doctor-note-form')).not.toBeInTheDocument()
  })

  it('should handle cancel form', () => {
    render(<DoctorNotesPage />)

    fireEvent.click(screen.getByText('Create Note'))
    fireEvent.click(screen.getByText('Cancel'))

    expect(screen.queryByTestId('doctor-note-form')).not.toBeInTheDocument()
  })

  it('should handle delete confirmation cancellation', async () => {
    render(<DoctorNotesPage />)

    // First select a note
    fireEvent.click(screen.getByText('Select Note'))
    
    // Mock window.confirm to return false
    window.confirm = jest.fn().mockReturnValue(false)
    
    // Try to delete
    fireEvent.click(screen.getByText('Delete Note'))

    // Should not call the API
    expect(mockFetch).not.toHaveBeenCalled()
  })

  it('should handle delete API error', async () => {
    mockFetch.mockResolvedValue({
      ok: false,
      json: async () => ({ success: false, error: 'Failed to delete' })
    } as Response)

    render(<DoctorNotesPage />)

    // First select a note
    fireEvent.click(screen.getByText('Select Note'))
    
    // Mock window.confirm
    window.confirm = jest.fn().mockReturnValue(true)
    
    // Then delete it
    fireEvent.click(screen.getByText('Delete Note'))

    const { toast } = require('sonner')
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to delete note')
    })
  })

  it('should show back to list button when viewing note', () => {
    render(<DoctorNotesPage />)

    fireEvent.click(screen.getByText('Select Note'))

    expect(screen.getByText('← Back to Notes')).toBeInTheDocument()
  })

  it('should handle back to list navigation', () => {
    render(<DoctorNotesPage />)

    // Select a note first
    fireEvent.click(screen.getByText('Select Note'))
    
    // Then go back
    fireEvent.click(screen.getByText('← Back to Notes'))

    expect(screen.queryByTestId('doctor-note-view')).not.toBeInTheDocument()
    expect(screen.getByTestId('doctor-notes-list')).toBeInTheDocument()
  })
})
