import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { useRouter, usePathname } from 'next/navigation'
import DoctorDashboard from '../../app/dashboard/doctor/page'

// Mock auth context
jest.mock('../../lib/hooks/use-auth', () => ({
  useAuth: () => ({
    user: {
      uid: 'test-doctor-id',
      email: '<EMAIL>',
      role: 'doctor',
      displayName: 'Dr. Test'
    },
    loading: false,
    signOut: jest.fn()
  })
}))

// Mock ProtectedRoute component
jest.mock('../../components/auth', () => ({
  ProtectedRoute: ({ children, allowedRoles }: any) => (
    <>{children}</>
  ),
}))

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(),
}))

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  Calendar: ({ className, ...props }: any) => <div data-testid="calendar-icon" className={className} {...props} />,
  Users: ({ className, ...props }: any) => <div data-testid="users-icon" className={className} {...props} />,
  FileText: ({ className, ...props }: any) => <div data-testid="file-text-icon" className={className} {...props} />,
  Clock: ({ className, ...props }: any) => <div data-testid="clock-icon" className={className} {...props} />,
  Plus: ({ className, ...props }: any) => <div data-testid="plus-icon" className={className} {...props} />,
  Search: ({ className, ...props }: any) => <div data-testid="search-icon" className={className} {...props} />,
  NotebookPen: ({ className, ...props }: any) => <div data-testid="notebook-pen-icon" className={className} {...props} />,
}))

// Mock utils
jest.mock('../../lib/utils', () => ({
  cn: jest.fn((...classes) => classes.filter(Boolean).join(' ')),
  formatDate: jest.fn((date) => '2024-01-15'),
  formatTime: jest.fn((time) => '10:00 AM'),
}))

// Mock UI components
jest.mock('../../components/ui', () => ({
  Card: ({ children, ...props }: any) => <div data-testid="card" {...props}>{children}</div>,
  CardContent: ({ children, ...props }: any) => <div data-testid="card-content" {...props}>{children}</div>,
  CardDescription: ({ children, ...props }: any) => <div data-testid="card-description" {...props}>{children}</div>,
  CardHeader: ({ children, ...props }: any) => <div data-testid="card-header" {...props}>{children}</div>,
  CardTitle: ({ children, ...props }: any) => <div data-testid="card-title" {...props}>{children}</div>,
  Button: ({ children, variant, className, ...props }: any) => (
    <button data-variant={variant} className={className} {...props}>{children}</button>
  ),
  Badge: ({ children, variant, ...props }: any) => (
    <span data-testid="badge" data-variant={variant} {...props}>{children}</span>
  ),
  Input: ({ ...props }: any) => <input data-testid="input" {...props} />,
}))


const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  prefetch: jest.fn(),
}

describe('Doctor Dashboard Page', () => {
  beforeEach(() => {
    if (!DoctorDashboard) {
      throw new Error('DoctorDashboard component not loaded')
    }
    jest.clearAllMocks()
      ; (useRouter as jest.Mock).mockReturnValue(mockRouter)
      ; (usePathname as jest.Mock).mockReturnValue('/dashboard/doctor')
  })

  describe('Component Structure', () => {
    it('should render within ProtectedRoute with admin and doctor roles', () => {
      render(<DoctorDashboard />)

      const protectedRoute = screen.getByTestId('protected-route')
      expect(protectedRoute).toBeInTheDocument()
      expect(protectedRoute).toHaveAttribute('data-allowed-roles', 'admin,doctor')
    })

    it('should render main dashboard header', () => {
      render(<DoctorDashboard />)

      expect(screen.getByText('Doctor Dashboard')).toBeInTheDocument()
      expect(screen.getByText('Manage your patients and appointments')).toBeInTheDocument()
    })

    it('should render header action buttons', () => {
      render(<DoctorDashboard />)

      expect(screen.getByText('View Schedule')).toBeInTheDocument()
      expect(screen.getByText('New Appointment')).toBeInTheDocument()

      // Check that buttons are clickable
      const scheduleButton = screen.getByText('View Schedule').closest('button')
      const appointmentButton = screen.getByText('New Appointment').closest('button')
      expect(scheduleButton).toBeInTheDocument()
      expect(appointmentButton).toBeInTheDocument()
    })
  })

  describe('Statistics Cards', () => {
    it('should render all statistics cards with correct data', () => {
      render(<DoctorDashboard />)

      // Check for statistics cards with actual component data
      expect(screen.getByText('My Patients')).toBeInTheDocument()
      expect(screen.getByText('45')).toBeInTheDocument()
      expect(screen.getByText('Total assigned patients')).toBeInTheDocument()

      // Use getAllByText for text that appears multiple times
      const todayAppointmentsTexts = screen.getAllByText("Today's Appointments")
      expect(todayAppointmentsTexts.length).toBeGreaterThan(0)
      expect(screen.getByText('8')).toBeInTheDocument()
      expect(screen.getByText('Scheduled for today')).toBeInTheDocument()

      expect(screen.getByText('Completed Today')).toBeInTheDocument()
      expect(screen.getByText('5')).toBeInTheDocument()
      expect(screen.getByText('Appointments completed')).toBeInTheDocument()

      expect(screen.getByText('Pending Records')).toBeInTheDocument()
      expect(screen.getByText('3')).toBeInTheDocument()
      expect(screen.getByText('Records to update')).toBeInTheDocument()
    })

    it('should render statistics icons correctly', () => {
      render(<DoctorDashboard />)

      // Use getAllByTestId for icons that appear multiple times
      const usersIcons = screen.getAllByTestId('users-icon')
      const calendarIcons = screen.getAllByTestId('calendar-icon')
      const clockIcons = screen.getAllByTestId('clock-icon')
      const fileTextIcons = screen.getAllByTestId('file-text-icon')

      expect(usersIcons.length).toBeGreaterThan(0)
      expect(calendarIcons.length).toBeGreaterThan(0)
      expect(clockIcons.length).toBeGreaterThan(0)
      expect(fileTextIcons.length).toBeGreaterThan(0)
    })
  })

  describe("Today's Appointments Section", () => {
    it('should render appointments card', () => {
      render(<DoctorDashboard />)

      // Use getAllByText for text that appears multiple times
      const todayAppointmentsTexts = screen.getAllByText("Today's Appointments")
      expect(todayAppointmentsTexts.length).toBeGreaterThan(0)
      expect(screen.getByText(/Your schedule for/)).toBeInTheDocument()
    })

    it('should render appointment list with correct data', () => {
      render(<DoctorDashboard />)

      // Check for actual appointments from mockTodayAppointments - use getAllByText for names that appear multiple times
      const johnDoeTexts = screen.getAllByText('John Doe')
      expect(johnDoeTexts.length).toBeGreaterThan(0)
      expect(screen.getByText('09:00 AM • 30 min')).toBeInTheDocument()

      const janeSmithTexts = screen.getAllByText('Jane Smith')
      expect(janeSmithTexts.length).toBeGreaterThan(0)
      expect(screen.getByText('10:30 AM • 15 min')).toBeInTheDocument()

      const robertJohnsonTexts = screen.getAllByText('Robert Johnson')
      expect(robertJohnsonTexts.length).toBeGreaterThan(0)
      expect(screen.getByText('02:00 PM • 30 min')).toBeInTheDocument()

      const emilyDavisTexts = screen.getAllByText('Emily Davis')
      expect(emilyDavisTexts.length).toBeGreaterThan(0)
      expect(screen.getByText('03:30 PM • 45 min')).toBeInTheDocument()
    })

    it('should render appointment badges for type and status', () => {
      render(<DoctorDashboard />)

      // Check for appointment type badges
      expect(screen.getByText('consultation')).toBeInTheDocument()
      expect(screen.getByText('follow-up')).toBeInTheDocument()
      expect(screen.getByText('routine')).toBeInTheDocument()
      expect(screen.getByText('emergency')).toBeInTheDocument()

      // Check for status badges
      const confirmedBadges = screen.getAllByText('confirmed')
      const pendingBadges = screen.getAllByText('pending')

      expect(confirmedBadges.length).toBeGreaterThan(0)
      expect(pendingBadges.length).toBeGreaterThan(0)
    })

    it('should render view full schedule button', () => {
      render(<DoctorDashboard />)

      const viewScheduleButton = screen.getByText('View Full Schedule')
      expect(viewScheduleButton).toBeInTheDocument()
      expect(viewScheduleButton.closest('button')).toHaveAttribute('data-variant', 'outline')
    })
  })

  describe('Recent Patients Section', () => {
    it('should render recent patients card', () => {
      render(<DoctorDashboard />)

      expect(screen.getByText('Recent Patients')).toBeInTheDocument()
      expect(screen.getByText("Patients you've seen recently")).toBeInTheDocument()
    })

    it('should render patient search functionality', () => {
      render(<DoctorDashboard />)

      const searchInput = screen.getByPlaceholderText('Search patients...')
      expect(searchInput).toBeInTheDocument()
      // Use getAllByTestId for icons that appear multiple times
      const searchIcons = screen.getAllByTestId('search-icon')
      expect(searchIcons.length).toBeGreaterThan(0)
    })

    it('should render patient list with correct data', () => {
      render(<DoctorDashboard />)

      // Check for actual patients from mockRecentPatients - use getAllByText for names that appear multiple times
      const johnDoeTexts = screen.getAllByText('John Doe')
      expect(johnDoeTexts.length).toBeGreaterThan(0)
      expect(screen.getByText('Age 45 • Last visit: 1/15/2024')).toBeInTheDocument()
      expect(screen.getByText('Hypertension')).toBeInTheDocument()

      const janeSmithTexts = screen.getAllByText('Jane Smith')
      expect(janeSmithTexts.length).toBeGreaterThan(0)
      expect(screen.getByText('Age 32 • Last visit: 1/14/2024')).toBeInTheDocument()
      expect(screen.getByText('Diabetes')).toBeInTheDocument()

      const robertJohnsonTexts = screen.getAllByText('Robert Johnson')
      expect(robertJohnsonTexts.length).toBeGreaterThan(0)
      expect(screen.getByText('Age 58 • Last visit: 1/13/2024')).toBeInTheDocument()
      expect(screen.getByText('Arthritis')).toBeInTheDocument()

      const emilyDavisTexts = screen.getAllByText('Emily Davis')
      expect(emilyDavisTexts.length).toBeGreaterThan(0)
      expect(screen.getByText('Age 28 • Last visit: 1/12/2024')).toBeInTheDocument()
      expect(screen.getByText('Migraine')).toBeInTheDocument()
    })

    it('should render patient status badges', () => {
      render(<DoctorDashboard />)

      // Check for condition status badges
      const stableBadges = screen.getAllByText('stable')
      const monitoringBadges = screen.getAllByText('monitoring')
      const improvingBadges = screen.getAllByText('improving')

      expect(stableBadges.length).toBeGreaterThan(0)
      expect(monitoringBadges.length).toBeGreaterThan(0)
      expect(improvingBadges.length).toBeGreaterThan(0)
    })

    it('should handle patient search', () => {
      render(<DoctorDashboard />)

      const searchInput = screen.getByPlaceholderText('Search patients...')

      // Test searching by name
      fireEvent.change(searchInput, { target: { value: 'John' } })
      expect(searchInput).toHaveValue('John')

      // Test searching by condition
      fireEvent.change(searchInput, { target: { value: 'Diabetes' } })
      expect(searchInput).toHaveValue('Diabetes')
    })

    it('should render view all patients button', () => {
      render(<DoctorDashboard />)

      const viewAllButton = screen.getByText('View All Patients')
      expect(viewAllButton).toBeInTheDocument()
      expect(viewAllButton.closest('button')).toHaveAttribute('data-variant', 'outline')
    })
  })

  describe('Quick Actions Section', () => {
    it('should render quick actions card', () => {
      render(<DoctorDashboard />)

      expect(screen.getByText('Quick Actions')).toBeInTheDocument()
      expect(screen.getByText('Common tasks and shortcuts')).toBeInTheDocument()
    })

    it('should render all quick action buttons', () => {
      render(<DoctorDashboard />)

      // Check for actual quick action buttons from component
      expect(screen.getByText('New Patient')).toBeInTheDocument()
      expect(screen.getByText('Schedule Appointment')).toBeInTheDocument()
      expect(screen.getByText('Add Medical Record')).toBeInTheDocument()
      expect(screen.getByText('Search Patients')).toBeInTheDocument()
    })

    it('should render quick action icons', () => {
      render(<DoctorDashboard />)

      // Icons should be present (multiple instances due to header and quick actions)
      const plusIcons = screen.getAllByTestId('plus-icon')
      const calendarIcons = screen.getAllByTestId('calendar-icon')
      const fileTextIcons = screen.getAllByTestId('file-text-icon')
      const searchIcons = screen.getAllByTestId('search-icon')

      expect(plusIcons.length).toBeGreaterThan(0)
      expect(calendarIcons.length).toBeGreaterThan(0)
      expect(fileTextIcons.length).toBeGreaterThan(0)
      expect(searchIcons.length).toBeGreaterThan(0)
    })

    it('should handle quick action button clicks', () => {
      render(<DoctorDashboard />)

      // Test clicking on quick action buttons
      const newPatientButton = screen.getByText('New Patient')
      const scheduleButton = screen.getByText('Schedule Appointment')
      const recordButton = screen.getByText('Add Medical Record')
      const searchButton = screen.getByText('Search Patients')

      expect(newPatientButton.closest('button')).toBeInTheDocument()
      expect(scheduleButton.closest('button')).toBeInTheDocument()
      expect(recordButton.closest('button')).toBeInTheDocument()
      expect(searchButton.closest('button')).toBeInTheDocument()

      // Should be clickable
      fireEvent.click(newPatientButton)
      fireEvent.click(scheduleButton)
      fireEvent.click(recordButton)
      fireEvent.click(searchButton)
      // In a real app, this would trigger navigation or modal
    })
  })

  describe('Responsive Layout', () => {
    it('should apply correct CSS classes for responsive design', () => {
      render(<DoctorDashboard />)

      // Check for responsive grid classes - need to find the actual grid container
      const statsGrid = screen.getByText('My Patients').closest('[data-testid="card"]')?.parentElement
      expect(statsGrid).toHaveClass('grid', 'gap-4', 'md:grid-cols-2', 'lg:grid-cols-4')

      // Use getAllByText for text that appears multiple times, then find the correct one
      const todayAppointmentsTexts = screen.getAllByText("Today's Appointments")
      const contentGrid = todayAppointmentsTexts[1]?.closest('[data-testid="card"]')?.parentElement
      expect(contentGrid).toHaveClass('grid', 'gap-6', 'md:grid-cols-2')

      // For quick actions, find the grid container that contains the buttons
      const quickActionsContainer = screen.getByText('New Patient').closest('button')?.parentElement
      expect(quickActionsContainer).toHaveClass('grid', 'gap-4', 'md:grid-cols-4')
    })
  })

  describe('Data Display', () => {
    it('should display formatted data correctly', () => {
      render(<DoctorDashboard />)

      // Numbers should be displayed as strings (actual component data)
      expect(screen.getByText('45')).toBeInTheDocument() // My Patients
      expect(screen.getByText('8')).toBeInTheDocument()  // Today's Appointments
      expect(screen.getByText('5')).toBeInTheDocument()  // Completed Today
      expect(screen.getByText('3')).toBeInTheDocument()  // Pending Records
    })

    it('should display patient initials correctly', () => {
      render(<DoctorDashboard />)

      // Patient initials should be displayed in avatar circles - use getAllByText for multiple instances
      const jdInitials = screen.getAllByText('JD') // John Doe
      const jsInitials = screen.getAllByText('JS') // Jane Smith
      const rjInitials = screen.getAllByText('RJ') // Robert Johnson
      const edInitials = screen.getAllByText('ED') // Emily Davis

      expect(jdInitials.length).toBeGreaterThan(0)
      expect(jsInitials.length).toBeGreaterThan(0)
      expect(rjInitials.length).toBeGreaterThan(0)
      expect(edInitials.length).toBeGreaterThan(0)
    })
  })
})
