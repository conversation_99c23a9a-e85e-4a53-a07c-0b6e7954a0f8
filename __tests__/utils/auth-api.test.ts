import { NextRequest } from 'next/server'
import { 
  authenticateUser, 
  hasRequiredRole, 
  canAccessDoctorFeatures,
  authenticateAndAuthorize,
  withDoctorAuth
} from '../../lib/utils/auth-api'
import { getDatabaseAdapter } from '../../lib/database'
import { User } from '../../lib/types'

// Mock Firebase configuration
jest.mock('../../lib/config/firebase', () => ({
  auth: {},
  db: {},
}))

// Mock Firebase app functions
jest.mock('firebase/app', () => ({
  getApps: jest.fn(() => []),
  initializeApp: jest.fn(() => ({})),
}))

// Mock Firebase auth functions
jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(() => ({})),
}))

// Mock dependencies
jest.mock('../../lib/database')

const mockDataAdapter = {
  getUserByFirebaseUid: jest.fn(),
}

const MockedGetDatabaseAdapter = getDatabaseAdapter as jest.MockedFunction<typeof getDatabaseAdapter>

beforeEach(() => {
  jest.clearAllMocks()
  MockedGetDatabaseAdapter.mockReturnValue(mockDataAdapter as any)
})

// Mock user data
const mockAdminUser: User = {
  uid: 'admin-uid',
  firebaseUid: 'firebase-admin-uid',
  email: '<EMAIL>',
  displayName: 'Admin User',
  role: 'admin',
  createdAt: new Date(),
  updatedAt: new Date()
}

const mockDoctorUser: User = {
  uid: 'doctor-uid',
  firebaseUid: 'firebase-doctor-uid',
  email: '<EMAIL>',
  displayName: 'Doctor User',
  role: 'doctor',
  createdAt: new Date(),
  updatedAt: new Date()
}

const mockPatientUser: User = {
  uid: 'patient-uid',
  firebaseUid: 'firebase-patient-uid',
  email: '<EMAIL>',
  displayName: 'Patient User',
  role: 'patient',
  createdAt: new Date(),
  updatedAt: new Date()
}

describe('Auth API Utils', () => {
  describe('authenticateUser', () => {
    it('should authenticate user with valid authorization header', async () => {
      const request = new NextRequest('http://localhost:3000/api/test', {
        headers: { authorization: 'firebase-admin-uid' }
      })

      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(mockAdminUser)

      const result = await authenticateUser(request)

      expect(result.success).toBe(true)
      expect(result.user).toEqual(mockAdminUser)
      expect(mockDataAdapter.getUserByFirebaseUid).toHaveBeenCalledWith('firebase-admin-uid')
    })

    it('should fail authentication without authorization header', async () => {
      const request = new NextRequest('http://localhost:3000/api/test')

      const result = await authenticateUser(request)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Authentication required')
      expect(result.status).toBe(401)
    })

    it('should fail authentication for non-existent user', async () => {
      const request = new NextRequest('http://localhost:3000/api/test', {
        headers: { authorization: 'invalid-uid' }
      })

      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(null)

      const result = await authenticateUser(request)

      expect(result.success).toBe(false)
      expect(result.error).toBe('User not found')
      expect(result.status).toBe(404)
    })
  })

  describe('hasRequiredRole', () => {
    it('should return true for admin user with admin role requirement', () => {
      const result = hasRequiredRole(mockAdminUser, ['admin'])
      expect(result).toBe(true)
    })

    it('should return true for doctor user with doctor role requirement', () => {
      const result = hasRequiredRole(mockDoctorUser, ['doctor'])
      expect(result).toBe(true)
    })

    it('should return true for admin user with multiple role requirements', () => {
      const result = hasRequiredRole(mockAdminUser, ['admin', 'doctor'])
      expect(result).toBe(true)
    })

    it('should return false for patient user with admin role requirement', () => {
      const result = hasRequiredRole(mockPatientUser, ['admin'])
      expect(result).toBe(false)
    })
  })

  describe('canAccessDoctorFeatures', () => {
    it('should allow admin access to doctor features', () => {
      const result = canAccessDoctorFeatures(mockAdminUser)
      expect(result).toBe(true)
    })

    it('should allow doctor access to doctor features', () => {
      const result = canAccessDoctorFeatures(mockDoctorUser)
      expect(result).toBe(true)
    })

    it('should deny patient access to doctor features', () => {
      const result = canAccessDoctorFeatures(mockPatientUser)
      expect(result).toBe(false)
    })
  })

  describe('authenticateAndAuthorize', () => {
    it('should succeed for admin user accessing doctor features', async () => {
      const request = new NextRequest('http://localhost:3000/api/test', {
        headers: { authorization: 'firebase-admin-uid' }
      })

      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(mockAdminUser)

      const result = await authenticateAndAuthorize(request, ['admin', 'doctor'])

      expect(result.success).toBe(true)
      expect(result.user).toEqual(mockAdminUser)
    })

    it('should fail for patient user accessing doctor features', async () => {
      const request = new NextRequest('http://localhost:3000/api/test', {
        headers: { authorization: 'firebase-patient-uid' }
      })

      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(mockPatientUser)

      const result = await authenticateAndAuthorize(request, ['admin', 'doctor'])

      expect(result.success).toBe(false)
      expect(result.error).toBe('Insufficient permissions')
      expect(result.status).toBe(403)
    })
  })

  describe('withDoctorAuth', () => {
    it('should create auth middleware for doctor features', async () => {
      const request = new NextRequest('http://localhost:3000/api/test', {
        headers: { authorization: 'firebase-doctor-uid' }
      })

      mockDataAdapter.getUserByFirebaseUid.mockResolvedValue(mockDoctorUser)

      const authMiddleware = withDoctorAuth()
      const result = await authMiddleware(request)

      expect(result.success).toBe(true)
      expect(result.user).toEqual(mockDoctorUser)
    })
  })
})
