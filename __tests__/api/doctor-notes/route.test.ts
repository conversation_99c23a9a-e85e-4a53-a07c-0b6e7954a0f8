import { NextRequest } from 'next/server'
import { GET, POST } from '../../../app/api/doctor-notes/route'
import { getDatabaseAdapter } from '../../../lib/database'
import { CreateDoctorNoteData } from '../../../lib/types'

// Mock the database
jest.mock('../../../lib/database', () => ({
  getDatabaseAdapter: jest.fn()
}))
const mockGetDatabaseAdapter = getDatabaseAdapter as jest.MockedFunction<typeof getDatabaseAdapter>

// Mock data
const mockDoctorNote = {
  id: 'note-1',
  patientId: 'patient-1',
  doctorId: 'doctor-1',
  title: 'Test Note',
  content: 'This is a test note content',
  noteType: 'general' as const,
  isPrivate: false,
  tags: ['test', 'general'],
  version: 1,
  isDeleted: false,
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z',
  appointmentId: null
}

const mockCreateNoteData: CreateDoctorNoteData = {
  patientId: 'patient-1',
  doctorId: 'doctor-1',
  title: 'Test Note',
  content: 'This is a test note content',
  noteType: 'general',
  isPrivate: false,
  tags: ['test', 'general']
}

describe('/api/doctor-notes', () => {
  let mockDb: any

  beforeEach(() => {
    mockDb = {
      getDoctorNotes: jest.fn(),
      createDoctorNote: jest.fn(),
      getDoctorById: jest.fn(),
      getPatientById: jest.fn()
    }
    mockGetDatabaseAdapter.mockReturnValue(mockDb)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/doctor-notes', () => {
    it('should return doctor notes with default pagination', async () => {
      const mockNotes = [mockDoctorNote]
      const mockPagination = {
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }

      mockDb.getDoctorNotes.mockResolvedValue({
        data: mockNotes,
        pagination: mockPagination
      })
      mockDb.getDoctorById.mockResolvedValue({
        id: 'doctor-1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      })

      const request = new NextRequest('http://localhost:3000/api/doctor-notes?doctorId=doctor-1')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toEqual(mockNotes)
      expect(data.pagination).toEqual(mockPagination)
      expect(mockDb.getDoctorNotes).toHaveBeenCalledWith({
        doctorId: 'doctor-1'
      }, {
        page: 1,
        limit: 10
      })
    })

    it('should return doctor notes with custom pagination and filters', async () => {
      const mockNotes = [mockDoctorNote]
      const mockPagination = {
        page: 2,
        limit: 5,
        total: 10,
        totalPages: 2,
        hasNext: false,
        hasPrev: true
      }

      mockDb.getDoctorNotes.mockResolvedValue({
        data: mockNotes,
        pagination: mockPagination
      })

      const request = new NextRequest(
        'http://localhost:3000/api/doctor-notes?doctorId=doctor-1&patientId=patient-1&page=2&limit=5&noteType=general&searchTerm=test'
      )
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(mockDb.getDoctorNotes).toHaveBeenCalledWith({
        doctorId: 'doctor-1',
        patientId: 'patient-1',
        noteType: 'general',
        searchTerm: 'test'
      }, {
        page: 2,
        limit: 5
      })
    })

    it('should return 400 if doctorId is missing', async () => {
      const request = new NextRequest('http://localhost:3000/api/doctor-notes')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Doctor ID is required')
    })

    it('should handle database errors', async () => {
      mockDb.getDoctorNotes.mockRejectedValue(new Error('Database error'))

      const request = new NextRequest('http://localhost:3000/api/doctor-notes?doctorId=doctor-1')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to get doctor notes')
    })
  })

  describe('POST /api/doctor-notes', () => {
    it('should create a new doctor note', async () => {
      mockDb.createDoctorNote.mockResolvedValue(mockDoctorNote)
      mockDb.getDoctorById.mockResolvedValue({
        id: 'doctor-1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      })
      mockDb.getPatientById.mockResolvedValue({
        id: 'patient-1',
        firstName: 'Jane',
        lastName: 'Doe',
        email: '<EMAIL>'
      })

      const request = new NextRequest('http://localhost:3000/api/doctor-notes', {
        method: 'POST',
        body: JSON.stringify(mockCreateNoteData),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toEqual(mockDoctorNote)
      expect(mockDb.createDoctorNote).toHaveBeenCalledWith(mockCreateNoteData)
    })

    it('should return 400 for invalid request body', async () => {
      const invalidData = {
        patientId: 'patient-1',
        // Missing required fields
      }

      const request = new NextRequest('http://localhost:3000/api/doctor-notes', {
        method: 'POST',
        body: JSON.stringify(invalidData),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('required')
    })

    it('should handle database errors during creation', async () => {
      // Setup successful doctor and patient lookups
      mockDb.getDoctorById.mockResolvedValue({
        id: 'doctor-1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      })
      mockDb.getPatientById.mockResolvedValue({
        id: 'patient-1',
        firstName: 'Jane',
        lastName: 'Doe',
        email: '<EMAIL>'
      })
      // But make createDoctorNote fail
      mockDb.createDoctorNote.mockRejectedValue(new Error('Database error'))

      const request = new NextRequest('http://localhost:3000/api/doctor-notes', {
        method: 'POST',
        body: JSON.stringify(mockCreateNoteData),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to create doctor note')
    })

    it('should handle malformed JSON', async () => {
      const request = new NextRequest('http://localhost:3000/api/doctor-notes', {
        method: 'POST',
        body: 'invalid json',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to create doctor note')
    })
  })
})
