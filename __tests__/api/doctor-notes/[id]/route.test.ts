import { NextRequest } from 'next/server'
import { GET, PUT, DELETE } from '../../../../app/api/doctor-notes/[id]/route'
import { getDatabaseAdapter } from '../../../../lib/database'
import { UpdateDoctorNoteData } from '../../../../lib/types'

// Mock the database
jest.mock('../../../../lib/database', () => ({
  getDatabaseAdapter: jest.fn()
}))
const mockGetDatabaseAdapter = getDatabaseAdapter as jest.MockedFunction<typeof getDatabaseAdapter>

// Mock data
const mockDoctorNote = {
  id: 'note-1',
  patientId: 'patient-1',
  doctorId: 'doctor-1',
  title: 'Test Note',
  content: 'This is a test note content',
  noteType: 'general' as const,
  isPrivate: false,
  tags: ['test', 'general'],
  version: 1,
  isDeleted: false,
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z',
  appointmentId: null
}

const mockUpdateData: UpdateDoctorNoteData = {
  title: 'Updated Test Note',
  content: 'This is updated content',
  noteType: 'consultation',
  isPrivate: true,
  tags: ['updated', 'consultation']
}

describe('/api/doctor-notes/[id]', () => {
  let mockDb: any
  const mockParams = { params: { id: 'note-1' } }

  beforeEach(() => {
    mockDb = {
      getDoctorNoteById: jest.fn(),
      updateDoctorNote: jest.fn(),
      deleteDoctorNote: jest.fn()
    }
    mockGetDatabaseAdapter.mockReturnValue(mockDb)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/doctor-notes/[id]', () => {
    it('should return a doctor note by id', async () => {
      mockDb.getDoctorNoteById.mockResolvedValue(mockDoctorNote)

      const request = new NextRequest('http://localhost:3000/api/doctor-notes/note-1?doctorId=doctor-1')
      const response = await GET(request, mockParams)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toEqual(mockDoctorNote)
      expect(mockDb.getDoctorNoteById).toHaveBeenCalledWith('note-1', 'doctor-1')
    })

    it('should return 400 if doctorId is missing', async () => {
      const request = new NextRequest('http://localhost:3000/api/doctor-notes/note-1')
      const response = await GET(request, mockParams)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Doctor ID is required')
    })

    it('should return 404 if note is not found', async () => {
      mockDb.getDoctorNoteById.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/doctor-notes/note-1?doctorId=doctor-1')
      const response = await GET(request, mockParams)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe('Doctor note not found')
    })

    it('should handle database errors', async () => {
      mockDb.getDoctorNoteById.mockRejectedValue(new Error('Database error'))

      const request = new NextRequest('http://localhost:3000/api/doctor-notes/note-1?doctorId=doctor-1')
      const response = await GET(request, mockParams)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to get doctor note')
    })
  })

  describe('PUT /api/doctor-notes/[id]', () => {
    it('should update a doctor note', async () => {
      const updatedNote = { ...mockDoctorNote, ...mockUpdateData, version: 2 }
      mockDb.getDoctorNoteById.mockResolvedValue(mockDoctorNote)
      mockDb.updateDoctorNote.mockResolvedValue(updatedNote)

      const request = new NextRequest('http://localhost:3000/api/doctor-notes/note-1', {
        method: 'PUT',
        body: JSON.stringify({ ...mockUpdateData, doctorId: 'doctor-1' }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await PUT(request, mockParams)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toEqual(updatedNote)
      expect(mockDb.updateDoctorNote).toHaveBeenCalledWith('note-1', mockUpdateData, 'doctor-1')
    })

    it('should return 400 if doctorId is missing', async () => {
      const request = new NextRequest('http://localhost:3000/api/doctor-notes/note-1', {
        method: 'PUT',
        body: JSON.stringify(mockUpdateData),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await PUT(request, mockParams)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Doctor ID is required')
    })

    it('should return 404 if note is not found', async () => {
      mockDb.updateDoctorNote.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/doctor-notes/note-1', {
        method: 'PUT',
        body: JSON.stringify({ ...mockUpdateData, doctorId: 'doctor-1' }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await PUT(request, mockParams)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Doctor note not found')
    })

    it('should handle malformed JSON', async () => {
      const request = new NextRequest('http://localhost:3000/api/doctor-notes/note-1', {
        method: 'PUT',
        body: 'invalid json',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await PUT(request, mockParams)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Invalid request body')
    })
  })

  describe('DELETE /api/doctor-notes/[id]', () => {
    it('should delete a doctor note', async () => {
      mockDb.deleteDoctorNote.mockResolvedValue(true)

      const request = new NextRequest('http://localhost:3000/api/doctor-notes/note-1?doctorId=doctor-1', {
        method: 'DELETE'
      })

      const response = await DELETE(request, mockParams)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toBe('Doctor note deleted successfully')
      expect(mockDb.deleteDoctorNote).toHaveBeenCalledWith('note-1', 'doctor-1')
    })

    it('should return 400 if doctorId is missing', async () => {
      const request = new NextRequest('http://localhost:3000/api/doctor-notes/note-1', {
        method: 'DELETE'
      })

      const response = await DELETE(request, mockParams)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Doctor ID is required')
    })

    it('should return 404 if note is not found', async () => {
      mockDb.deleteDoctorNote.mockResolvedValue(false)

      const request = new NextRequest('http://localhost:3000/api/doctor-notes/note-1?doctorId=doctor-1', {
        method: 'DELETE'
      })

      const response = await DELETE(request, mockParams)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Doctor note not found')
    })

    it('should handle database errors', async () => {
      mockDb.deleteDoctorNote.mockRejectedValue(new Error('Database error'))

      const request = new NextRequest('http://localhost:3000/api/doctor-notes/note-1?doctorId=doctor-1', {
        method: 'DELETE'
      })

      const response = await DELETE(request, mockParams)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Failed to delete doctor note')
    })
  })
})
