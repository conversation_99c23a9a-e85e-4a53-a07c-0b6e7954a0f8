import React from 'react'
import { render } from '@testing-library/react'

// Minimal test to debug the component import issue
describe('Debug Doctor Dashboard Import', () => {
  it('should be able to import the component', async () => {
    try {
      const module = await import('../app/dashboard/doctor/page')
      console.log('Module:', module)
      console.log('Default export:', module.default)
      console.log('Type of default:', typeof module.default)
      
      const Component = module.default
      expect(Component).toBeDefined()
      expect(typeof Component).toBe('function')
    } catch (error) {
      console.error('Import error:', error)
      throw error
    }
  })

  it('should be able to render a simple version', async () => {
    // Mock only the absolute minimum
    jest.mock('next/navigation', () => ({
      useRouter: () => ({ push: jest.fn() }),
      usePathname: () => '/dashboard/doctor'
    }))

    try {
      const module = await import('../app/dashboard/doctor/page')
      const Component = module.default
      
      console.log('About to render component:', Component)
      
      // Try to render without any complex setup
      const { container } = render(<Component />)
      console.log('Render successful, container:', container.innerHTML.substring(0, 100))
    } catch (error) {
      console.error('Render error:', error)
      throw error
    }
  })
})
