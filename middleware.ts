import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Define protected routes and their required roles
const protectedRoutes = {
  '/dashboard': ['admin', 'doctor', 'patient'],
  '/dashboard/admin': ['admin'],
  '/dashboard/doctor': ['admin', 'doctor'], // Allow admin access to doctor features
  '/dashboard/patient': ['patient'],
  '/dashboard/patients': ['admin', 'doctor'],
  '/dashboard/doctors': ['admin'],
  '/dashboard/invite-doctor': ['admin'],
  '/dashboard/appointments': ['admin', 'doctor', 'patient'],
  '/dashboard/medical-records': ['doctor', 'patient'],
  '/dashboard/profile': ['admin', 'doctor', 'patient'],
  '/dashboard/settings': ['admin', 'doctor', 'patient'],
}

// Public routes that don't require authentication
const publicRoutes = [
  '/',
  '/login',
]

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Check if the route is public
  if (publicRoutes.includes(pathname)) {
    return NextResponse.next()
  }

  // Check if the route is protected
  const isProtectedRoute = Object.keys(protectedRoutes).some(route => 
    pathname.startsWith(route)
  )

  if (isProtectedRoute) {
    // In a real application, you would verify the JWT token here
    // For now, we'll let the client-side handle authentication
    // and redirect in the dashboard layout
    return NextResponse.next()
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
