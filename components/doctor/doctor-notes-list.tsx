'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Badge } from '../ui/badge'
import { LoadingSpinner } from '../ui/loading-spinner'
import { DoctorNote, PaginatedResponse } from '../../lib/types'
import { toast } from 'sonner'

interface DoctorNotesListProps {
  doctorId: string
  patientId?: string
  onNoteSelect?: (note: DoctorNote) => void
  onCreateNote?: () => void
}

export function DoctorNotesList({ 
  doctorId, 
  patientId, 
  onNoteSelect, 
  onCreateNote 
}: DoctorNotesListProps) {
  const [notes, setNotes] = useState<DoctorNote[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState<PaginatedResponse<DoctorNote>['pagination'] | null>(null)

  const fetchNotes = async (page = 1, search = '') => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        doctorId,
        page: page.toString(),
        limit: '10'
      })

      if (patientId) params.append('patientId', patientId)
      if (search) params.append('searchTerm', search)

      const response = await fetch(`/api/doctor-notes?${params}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch notes')
      }

      setNotes(data.data)
      setPagination(data.pagination)
    } catch (error) {
      console.error('Error fetching notes:', error)
      toast.error('Failed to load doctor notes')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchNotes(currentPage, searchTerm)
  }, [doctorId, patientId, currentPage])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchNotes(1, searchTerm)
  }

  const handleDeleteNote = async (noteId: string) => {
    if (!confirm('Are you sure you want to delete this note?')) return

    try {
      const response = await fetch(`/api/doctor-notes/${noteId}?doctorId=${doctorId}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete note')
      }

      toast.success('Note deleted successfully')
      fetchNotes(currentPage, searchTerm)
    } catch (error) {
      console.error('Error deleting note:', error)
      toast.error('Failed to delete note')
    }
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getNoteTypeColor = (noteType: string) => {
    switch (noteType) {
      case 'consultation': return 'bg-blue-100 text-blue-800'
      case 'follow-up': return 'bg-green-100 text-green-800'
      case 'emergency': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading && notes.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Doctor Notes</h2>
        {onCreateNote && (
          <Button onClick={onCreateNote}>
            Create New Note
          </Button>
        )}
      </div>

      <form onSubmit={handleSearch} className="flex gap-2">
        <Input
          placeholder="Search notes..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="flex-1"
        />
        <Button type="submit">Search</Button>
      </form>

      {notes.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-500">No notes found</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {notes.map((note) => (
            <Card key={note.id} className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{note.title}</CardTitle>
                  <div className="flex gap-2">
                    <Badge className={getNoteTypeColor(note.noteType)}>
                      {note.noteType}
                    </Badge>
                    {note.isPrivate && (
                      <Badge variant="outline">Private</Badge>
                    )}
                  </div>
                </div>
                <p className="text-sm text-gray-500">
                  {formatDate(note.createdAt)}
                  {note.updatedAt !== note.createdAt && (
                    <span> • Updated {formatDate(note.updatedAt)}</span>
                  )}
                </p>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 line-clamp-3 mb-4">
                  {note.content}
                </p>
                {note.tags && note.tags.length > 0 && (
                  <div className="flex gap-1 mb-4">
                    {note.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">
                    Version {note.version}
                  </span>
                  <div className="flex gap-2">
                    {onNoteSelect && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onNoteSelect(note)}
                      >
                        View
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteNote(note.id)}
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {pagination && pagination.totalPages > 1 && (
        <div className="flex justify-center gap-2 mt-6">
          <Button
            variant="outline"
            disabled={!pagination.hasPrev}
            onClick={() => setCurrentPage(currentPage - 1)}
          >
            Previous
          </Button>
          <span className="flex items-center px-4">
            Page {pagination.page} of {pagination.totalPages}
          </span>
          <Button
            variant="outline"
            disabled={!pagination.hasNext}
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}
