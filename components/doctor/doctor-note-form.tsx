'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { LoadingSpinner } from '../ui/loading-spinner'
import { DoctorNote, CreateDoctorNoteData, UpdateDoctorNoteData } from '../../lib/types'
import { toast } from 'sonner'

interface DoctorNoteFormProps {
  doctorId: string
  patientId: string
  note?: DoctorNote
  onSave?: (note: DoctorNote) => void
  onCancel?: () => void
}

export function DoctorNoteForm({ 
  doctorId, 
  patientId, 
  note, 
  onSave, 
  onCancel 
}: DoctorNoteFormProps) {
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    noteType: 'general' as 'general' | 'consultation' | 'follow-up' | 'emergency',
    isPrivate: false,
    tags: [] as string[]
  })
  const [tagInput, setTagInput] = useState('')
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (note) {
      setFormData({
        title: note.title,
        content: note.content,
        noteType: note.noteType,
        isPrivate: note.isPrivate,
        tags: note.tags || []
      })
    }
  }, [note])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim() || !formData.content.trim()) {
      toast.error('Title and content are required')
      return
    }

    setLoading(true)

    try {
      if (note) {
        // Update existing note
        const updateData: UpdateDoctorNoteData = {
          title: formData.title,
          content: formData.content,
          noteType: formData.noteType,
          isPrivate: formData.isPrivate,
          tags: formData.tags
        }

        const response = await fetch(`/api/doctor-notes/${note.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ ...updateData, doctorId })
        })

        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.error || 'Failed to update note')
        }

        toast.success('Note updated successfully')
        onSave?.(data.data)
      } else {
        // Create new note
        const createData: CreateDoctorNoteData = {
          patientId,
          doctorId,
          title: formData.title,
          content: formData.content,
          noteType: formData.noteType,
          isPrivate: formData.isPrivate,
          tags: formData.tags
        }

        const response = await fetch('/api/doctor-notes', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(createData)
        })

        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.error || 'Failed to create note')
        }

        toast.success('Note created successfully')
        onSave?.(data.data)
      }
    } catch (error) {
      console.error('Error saving note:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to save note')
    } finally {
      setLoading(false)
    }
  }

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }))
      setTagInput('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddTag()
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {note ? 'Edit Note' : 'Create New Note'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Enter note title"
              required
            />
          </div>

          <div>
            <Label htmlFor="noteType">Note Type</Label>
            <select
              id="noteType"
              value={formData.noteType}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                noteType: e.target.value as typeof formData.noteType 
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="general">General</option>
              <option value="consultation">Consultation</option>
              <option value="follow-up">Follow-up</option>
              <option value="emergency">Emergency</option>
            </select>
          </div>

          <div>
            <Label htmlFor="content">Content</Label>
            <textarea
              id="content"
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              placeholder="Enter note content"
              rows={8}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <Label htmlFor="tags">Tags</Label>
            <div className="flex gap-2 mb-2">
              <Input
                id="tags"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Add a tag"
              />
              <Button type="button" onClick={handleAddTag}>
                Add
              </Button>
            </div>
            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isPrivate"
              checked={formData.isPrivate}
              onChange={(e) => setFormData(prev => ({ ...prev, isPrivate: e.target.checked }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <Label htmlFor="isPrivate">Private note (only visible to you)</Label>
          </div>

          <div className="flex gap-2 pt-4">
            <Button type="submit" disabled={loading}>
              {loading ? <LoadingSpinner /> : (note ? 'Update Note' : 'Create Note')}
            </Button>
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
