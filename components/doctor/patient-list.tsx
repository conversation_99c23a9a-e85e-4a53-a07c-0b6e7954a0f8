'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Badge } from '../ui/badge'
import { LoadingSpinner } from '../ui/loading-spinner'
import { Patient, PaginatedResponse } from '../../lib/types'
import { toast } from 'sonner'

interface PatientListProps {
  doctorId: string
  onPatientSelect?: (patient: Patient) => void
  onCreatePatient?: () => void
  showCreateButton?: boolean
}

export function PatientList({ 
  doctorId, 
  onPatientSelect, 
  onCreatePatient,
  showCreateButton = true
}: PatientListProps) {
  const [patients, setPatients] = useState<Patient[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState<PaginatedResponse<Patient>['pagination'] | null>(null)

  const fetchPatients = async (page = 1, search = '') => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        doctorId,
        page: page.toString(),
        limit: '10'
      })

      // Note: Search functionality would need to be implemented in the API

      const response = await fetch(`/api/patients?${params}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch patients')
      }

      setPatients(data.data)
      setPagination(data.pagination)
    } catch (error) {
      console.error('Error fetching patients:', error)
      toast.error('Failed to load patients')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPatients(currentPage, searchTerm)
  }, [doctorId, currentPage])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchPatients(1, searchTerm)
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const calculateAge = (dateOfBirth: Date) => {
    const today = new Date()
    const birthDate = new Date(dateOfBirth)
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }
    
    return age
  }

  const getGenderColor = (gender: string) => {
    switch (gender.toLowerCase()) {
      case 'male': return 'bg-blue-100 text-blue-800'
      case 'female': return 'bg-pink-100 text-pink-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading && patients.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">My Patients</h2>
        {showCreateButton && onCreatePatient && (
          <Button onClick={onCreatePatient}>
            Add New Patient
          </Button>
        )}
      </div>

      <form onSubmit={handleSearch} className="flex gap-2">
        <Input
          placeholder="Search patients by name or email..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="flex-1"
        />
        <Button type="submit">Search</Button>
      </form>

      {patients.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-500">No patients found</p>
            {showCreateButton && onCreatePatient && (
              <Button onClick={onCreatePatient} className="mt-4">
                Add Your First Patient
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {patients.map((patient) => (
            <Card 
              key={patient.id} 
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => onPatientSelect?.(patient)}
            >
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">
                  {patient.firstName} {patient.lastName}
                </CardTitle>
                <div className="flex gap-2">
                  <Badge className={getGenderColor(patient.gender)}>
                    {patient.gender}
                  </Badge>
                  <Badge variant="outline">
                    Age {calculateAge(patient.dateOfBirth)}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium">Email:</span> {patient.email}
                  </div>
                  {patient.phone && (
                    <div>
                      <span className="font-medium">Phone:</span> {patient.phone}
                    </div>
                  )}
                  <div>
                    <span className="font-medium">DOB:</span> {formatDate(patient.dateOfBirth)}
                  </div>
                  {patient.emergencyContact?.name && (
                    <div>
                      <span className="font-medium">Emergency Contact:</span> {patient.emergencyContact.name}
                    </div>
                  )}
                </div>
                
                <div className="mt-4 pt-4 border-t">
                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <span>Patient since {formatDate(patient.createdAt)}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        onPatientSelect?.(patient)
                      }}
                    >
                      View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {pagination && pagination.totalPages > 1 && (
        <div className="flex justify-center gap-2 mt-6">
          <Button
            variant="outline"
            disabled={!pagination.hasPrev}
            onClick={() => setCurrentPage(currentPage - 1)}
          >
            Previous
          </Button>
          <span className="flex items-center px-4">
            Page {pagination.page} of {pagination.totalPages}
          </span>
          <Button
            variant="outline"
            disabled={!pagination.hasNext}
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}
