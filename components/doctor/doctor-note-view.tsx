'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { DoctorNote } from '../../lib/types'
import { toast } from 'sonner'

interface DoctorNoteViewProps {
  note: DoctorNote
  doctorId: string
  onEdit?: (note: Doctor<PERSON><PERSON>) => void
  onDelete?: (noteId: string) => void
  onClose?: () => void
}

export function DoctorNoteView({ 
  note, 
  doctorId, 
  onEdit, 
  onDelete, 
  onClose 
}: DoctorNoteViewProps) {
  const [loading, setLoading] = useState(false)

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getNoteTypeColor = (noteType: string) => {
    switch (noteType) {
      case 'consultation': return 'bg-blue-100 text-blue-800'
      case 'follow-up': return 'bg-green-100 text-green-800'
      case 'emergency': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this note? This action cannot be undone.')) {
      return
    }

    setLoading(true)

    try {
      const response = await fetch(`/api/doctor-notes/${note.id}?doctorId=${doctorId}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete note')
      }

      toast.success('Note deleted successfully')
      onDelete?.(note.id)
    } catch (error) {
      console.error('Error deleting note:', error)
      toast.error('Failed to delete note')
    } finally {
      setLoading(false)
    }
  }

  const canEdit = note.doctorId === doctorId

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <CardTitle className="text-2xl mb-2">{note.title}</CardTitle>
            <div className="flex gap-2 mb-2">
              <Badge className={getNoteTypeColor(note.noteType)}>
                {note.noteType}
              </Badge>
              {note.isPrivate && (
                <Badge variant="outline">Private</Badge>
              )}
            </div>
            <div className="text-sm text-gray-500 space-y-1">
              <p>Created: {formatDate(note.createdAt)}</p>
              {note.updatedAt !== note.createdAt && (
                <p>Last updated: {formatDate(note.updatedAt)}</p>
              )}
              <p>Version: {note.version}</p>
            </div>
          </div>
          <div className="flex gap-2">
            {canEdit && onEdit && (
              <Button
                variant="outline"
                onClick={() => onEdit(note)}
                disabled={loading}
              >
                Edit
              </Button>
            )}
            {canEdit && onDelete && (
              <Button
                variant="outline"
                onClick={handleDelete}
                disabled={loading}
              >
                Delete
              </Button>
            )}
            {onClose && (
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3">Content</h3>
            <div className="prose max-w-none">
              <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                {note.content}
              </div>
            </div>
          </div>

          {note.tags && note.tags.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-3">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {note.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {note.appointmentId && (
            <div>
              <h3 className="text-lg font-semibold mb-3">Related Information</h3>
              <p className="text-sm text-gray-600">
                Appointment ID: {note.appointmentId}
              </p>
            </div>
          )}

          <div className="border-t pt-4">
            <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
              <div>
                <span className="font-medium">Patient ID:</span> {note.patientId}
              </div>
              <div>
                <span className="font-medium">Doctor ID:</span> {note.doctorId}
              </div>
              <div>
                <span className="font-medium">Note ID:</span> {note.id}
              </div>
              <div>
                <span className="font-medium">Privacy:</span> {note.isPrivate ? 'Private' : 'Shared'}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
