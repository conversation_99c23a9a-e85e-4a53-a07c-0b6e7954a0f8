'use client'

import React, { useState } from 'react'
import { UserPlus, Mail, User, Stethoscope } from 'lucide-react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Button,
  Input,
  Alert,
  AlertDescription,
  LoadingSpinner,
  useToast
} from '../ui'
import { useAuth } from '../../lib/hooks'
import { validateEmail } from '../../lib/utils/validation'

interface InviteDoctorModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

interface DoctorInviteForm {
  email: string
  firstName: string
  lastName: string
  specialization: string
}

interface FormErrors {
  email?: string
  firstName?: string
  lastName?: string
  specialization?: string
}

const InviteDoctorModal: React.FC<InviteDoctorModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const { user } = useAuth()
  const { addToast } = useToast()
  const [formData, setFormData] = useState<DoctorInviteForm>({
    email: '',
    firstName: '',
    lastName: '',
    specialization: ''
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [submitSuccess, setSubmitSuccess] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear error for this field when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required'
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    // First name validation
    if (!formData.firstName) {
      newErrors.firstName = 'First name is required'
    } else if (formData.firstName.length < 2) {
      newErrors.firstName = 'First name must be at least 2 characters'
    }

    // Last name validation
    if (!formData.lastName) {
      newErrors.lastName = 'Last name is required'
    } else if (formData.lastName.length < 2) {
      newErrors.lastName = 'Last name must be at least 2 characters'
    }

    // Specialization validation
    if (!formData.specialization) {
      newErrors.specialization = 'Specialization is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm() || !user) {
      return
    }

    setIsSubmitting(true)
    setSubmitError(null)

    try {
      const response = await fetch('/api/invitations/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          invitedByUid: user.uid,
          doctorInfo: {
            firstName: formData.firstName,
            lastName: formData.lastName,
            specialization: formData.specialization
          }
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send invitation')
      }

      // Show success toast
      addToast({
        title: 'Invitation Sent!',
        description: `Doctor invitation sent to ${formData.email}`,
      })

      // Reset form
      setFormData({
        email: '',
        firstName: '',
        lastName: '',
        specialization: ''
      })

      // Call success callback and close modal
      onSuccess?.()
      onClose()

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send invitation'
      setSubmitError(errorMessage)

      // Show error toast
      addToast({
        title: 'Failed to Send Invitation',
        description: errorMessage,
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      setFormData({
        email: '',
        firstName: '',
        lastName: '',
        specialization: ''
      })
      setErrors({})
      setSubmitError(null)
      setSubmitSuccess(false)
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Invite Doctor</DialogTitle>
          <DialogDescription>
            Send an invitation to a new doctor to join the system
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          {submitError && (
            <Alert variant="destructive">
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-2 gap-4">
            <Input
              label="First Name"
              name="firstName"
              value={formData.firstName}
              onChange={handleInputChange}
              error={errors.firstName}
              placeholder="Enter first name"
              disabled={isSubmitting}
              required
            />

            <Input
              label="Last Name"
              name="lastName"
              value={formData.lastName}
              onChange={handleInputChange}
              error={errors.lastName}
              placeholder="Enter last name"
              disabled={isSubmitting}
              required
            />
          </div>

          <Input
            label="Email Address"
            type="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            error={errors.email}
            placeholder="<EMAIL>"
            disabled={isSubmitting}
            required
          />

          <Input
            label="Specialization"
            name="specialization"
            value={formData.specialization}
            onChange={handleInputChange}
            error={errors.specialization}
            placeholder="e.g., Cardiology, Pediatrics, Internal Medicine"
            disabled={isSubmitting}
            required
          />

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Sending Invitation...
                </>
              ) : (
                <>
                  <UserPlus className="w-4 h-4 mr-2" />
                  Send Invitation
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default InviteDoctorModal
