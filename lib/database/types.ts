import {
  User,
  User<PERSON><PERSON>,
  Patient,
  Doctor,
  Appoint<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ientDoctorAssignment,
  CreateDoctorNoteData,
  UpdateDoctorNoteData,
  DoctorNoteFilters,
  CreatePatientDoctorAssignmentData,
  UpdatePatientDoctorAssignmentData,
  PaginationParams,
  PaginatedResponse
} from '../types'

export interface DataAdapter {
  // User operations
  createUser(userData: Omit<User, 'uid'> & { firebaseUid: string }): Promise<User>
  getUserByFirebaseUid(firebaseUid: string): Promise<User | null>
  getUserById(id: string): Promise<User | null>
  updateUserRole(id: string, role: UserRole): Promise<void>
  
  // Doctor invitation operations
  createDoctorInvitation(email: string, invitedBy: string): Promise<{ token: string; expiresAt: Date }>
  getDoctorInvitationByToken(token: string): Promise<any | null>
  markInvitationAsUsed(token: string): Promise<void>
  
  // Patient operations
  createPatient(patientData: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>): Promise<Patient>
  getPatientById(id: string): Promise<Patient | null>
  getPatientByUserId(userId: string): Promise<Patient | null>
  updatePatient(id: string, patientData: Partial<Patient>): Promise<Patient>
  deletePatient(id: string): Promise<void>
  getPatients(params?: PaginationParams): Promise<PaginatedResponse<Patient>>
  
  // Doctor operations
  createDoctor(doctorData: Omit<Doctor, 'id' | 'createdAt' | 'updatedAt'>): Promise<Doctor>
  getDoctorById(id: string): Promise<Doctor | null>
  getDoctorByUserId(userId: string): Promise<Doctor | null>
  updateDoctor(id: string, doctorData: Partial<Doctor>): Promise<Doctor>
  deleteDoctor(id: string): Promise<void>
  getDoctors(params?: PaginationParams): Promise<PaginatedResponse<Doctor>>
  
  // Appointment operations
  createAppointment(appointmentData: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>): Promise<Appointment>
  getAppointmentById(id: string): Promise<Appointment | null>
  updateAppointment(id: string, appointmentData: Partial<Appointment>): Promise<Appointment>
  deleteAppointment(id: string): Promise<void>
  getAppointments(params?: PaginationParams): Promise<PaginatedResponse<Appointment>>
  getAppointmentsByPatient(patientId: string, params?: PaginationParams): Promise<PaginatedResponse<Appointment>>
  getAppointmentsByDoctor(doctorId: string, params?: PaginationParams): Promise<PaginatedResponse<Appointment>>
  
  // Medical Record operations
  createMedicalRecord(recordData: Omit<MedicalRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<MedicalRecord>
  getMedicalRecordById(id: string): Promise<MedicalRecord | null>
  updateMedicalRecord(id: string, recordData: Partial<MedicalRecord>): Promise<MedicalRecord>
  deleteMedicalRecord(id: string): Promise<void>
  getMedicalRecords(params?: PaginationParams): Promise<PaginatedResponse<MedicalRecord>>
  getMedicalRecordsByPatient(patientId: string, params?: PaginationParams): Promise<PaginatedResponse<MedicalRecord>>

  // Doctor Notes operations
  createDoctorNote(noteData: CreateDoctorNoteData): Promise<DoctorNote>
  getDoctorNoteById(id: string): Promise<DoctorNote | null>
  updateDoctorNote(id: string, noteData: UpdateDoctorNoteData): Promise<DoctorNote>
  deleteDoctorNote(id: string): Promise<void> // Soft delete
  getDoctorNotes(filters?: DoctorNoteFilters, params?: PaginationParams): Promise<PaginatedResponse<DoctorNote>>
  getDoctorNotesByPatient(patientId: string, doctorId?: string, params?: PaginationParams): Promise<PaginatedResponse<DoctorNote>>
  getDoctorNotesByDoctor(doctorId: string, params?: PaginationParams): Promise<PaginatedResponse<DoctorNote>>
  searchDoctorNotes(searchTerm: string, doctorId?: string, params?: PaginationParams): Promise<PaginatedResponse<DoctorNote>>

  // Patient-Doctor Assignment operations
  createPatientDoctorAssignment(assignmentData: CreatePatientDoctorAssignmentData): Promise<PatientDoctorAssignment>
  getPatientDoctorAssignmentById(id: string): Promise<PatientDoctorAssignment | null>
  updatePatientDoctorAssignment(id: string, assignmentData: UpdatePatientDoctorAssignmentData): Promise<PatientDoctorAssignment>
  deletePatientDoctorAssignment(id: string): Promise<void>
  getPatientDoctorAssignments(patientId?: string, doctorId?: string, params?: PaginationParams): Promise<PaginatedResponse<PatientDoctorAssignment>>
  getPatientsByDoctor(doctorId: string, params?: PaginationParams): Promise<PaginatedResponse<Patient>>
  getDoctorsByPatient(patientId: string, params?: PaginationParams): Promise<PaginatedResponse<Doctor>>
  assignPatientToDoctor(patientId: string, doctorId: string, assignedBy: string, isPrimary?: boolean, notes?: string): Promise<PatientDoctorAssignment>
  unassignPatientFromDoctor(patientId: string, doctorId: string): Promise<void>
}

export type DatabaseType = 'postgresql' | 'firebase'

export interface DatabaseConfig {
  type: DatabaseType
  connectionString?: string
  firebaseConfig?: any
}
