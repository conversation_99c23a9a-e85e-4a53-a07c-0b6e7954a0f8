import { eq, and, desc, asc, gte, lte, like, count, or, ilike } from 'drizzle-orm'
import { db } from '../config/database'
import {
  users,
  doctorInvitations,
  patients,
  doctors,
  appointments,
  medicalRecords,
  doctorNotes,
  patientDoctorAssignments
} from '../config/schema'
import {
  User,
  User<PERSON><PERSON>,
  Patient,
  Doctor,
  Appointment,
  MedicalR<PERSON>ord,
  Doctor<PERSON><PERSON>,
  PatientDoctorAssignment,
  CreateDoctorNoteData,
  UpdateDoctorNoteData,
  DoctorNoteFilters,
  CreatePatientDoctorAssignmentData,
  UpdatePatientDoctorAssignmentData,
  PaginationParams,
  PaginatedResponse
} from '../types'
import crypto from 'crypto-js'
import { DataAdapter } from './types'

export class PostgresDataAdapter implements DataAdapter {
  // User operations
  async createUser(userData: Omit<User, 'uid'> & { firebaseUid: string }): Promise<User> {
    try {
      const [user] = await db.insert(users).values({
        firebaseUid: userData.firebaseUid,
        email: userData.email,
        displayName: userData.displayName || 'User',
        role: userData.role,
      }).returning()

      return {
        uid: user.id,
        email: user.email,
        displayName: user.displayName,
        role: user.role as UserRole,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      }
    } catch (error) {
      console.error('Error creating user:', error)
      throw new Error('Failed to create user')
    }
  }

  async getUserByFirebaseUid(firebaseUid: string): Promise<User | null> {
    try {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.firebaseUid, firebaseUid))
        .limit(1)

      if (!user) return null

      return {
        uid: user.id,
        email: user.email,
        displayName: user.displayName,
        role: user.role as UserRole,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      }
    } catch (error) {
      console.error('Error getting user by Firebase UID:', error)
      throw new Error('Failed to get user')
    }
  }

  async getUserById(id: string): Promise<User | null> {
    try {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1)

      if (!user) return null

      return {
        uid: user.id,
        email: user.email,
        displayName: user.displayName,
        role: user.role as UserRole,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      }
    } catch (error) {
      console.error('Error getting user by ID:', error)
      throw new Error('Failed to get user')
    }
  }

  async updateUserRole(id: string, role: UserRole): Promise<void> {
    try {
      await db
        .update(users)
        .set({ 
          role,
          updatedAt: new Date()
        })
        .where(eq(users.id, id))
    } catch (error) {
      console.error('Error updating user role:', error)
      throw new Error('Failed to update user role')
    }
  }

  // Doctor invitation operations
  async createDoctorInvitation(email: string, invitedBy: string): Promise<{ token: string; expiresAt: Date }> {
    try {
      const token = crypto.lib.WordArray.random(32).toString()
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 7) // 7 days from now

      await db.insert(doctorInvitations).values({
        email,
        token,
        invitedBy,
        expiresAt,
        isUsed: false,
      })

      return { token, expiresAt }
    } catch (error) {
      console.error('Error creating doctor invitation:', error)
      throw new Error('Failed to create doctor invitation')
    }
  }

  async getDoctorInvitationByToken(token: string): Promise<any | null> {
    try {
      const [invitation] = await db
        .select()
        .from(doctorInvitations)
        .where(eq(doctorInvitations.token, token))
        .limit(1)

      return invitation || null
    } catch (error) {
      console.error('Error getting doctor invitation by token:', error)
      throw new Error('Failed to get doctor invitation')
    }
  }

  async markInvitationAsUsed(token: string): Promise<void> {
    try {
      await db
        .update(doctorInvitations)
        .set({
          isUsed: true,
          usedAt: new Date()
        })
        .where(eq(doctorInvitations.token, token))
    } catch (error) {
      console.error('Error marking invitation as used:', error)
      throw new Error('Failed to mark invitation as used')
    }
  }

  // Patient operations - Placeholder implementation
  async createPatient(_patientData: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>): Promise<Patient> {
    throw new Error('Method not implemented - Patient operations need schema alignment')
  }

  async getPatientById(_id: string): Promise<Patient | null> {
    throw new Error('Method not implemented - Patient operations need schema alignment')
  }

  async getPatientByUserId(_userId: string): Promise<Patient | null> {
    throw new Error('Method not implemented - Patient operations need schema alignment')
  }

  // Placeholder implementations for remaining methods
  async updatePatient(id: string, patientData: Partial<Patient>): Promise<Patient> {
    throw new Error('Method not implemented')
  }

  async deletePatient(id: string): Promise<void> {
    throw new Error('Method not implemented')
  }

  async getPatients(params?: PaginationParams): Promise<PaginatedResponse<Patient>> {
    throw new Error('Method not implemented')
  }

  async createDoctor(doctorData: Omit<Doctor, 'id' | 'createdAt' | 'updatedAt'>): Promise<Doctor> {
    throw new Error('Method not implemented')
  }

  async getDoctorById(id: string): Promise<Doctor | null> {
    throw new Error('Method not implemented')
  }

  async getDoctorByUserId(userId: string): Promise<Doctor | null> {
    throw new Error('Method not implemented')
  }

  async updateDoctor(id: string, doctorData: Partial<Doctor>): Promise<Doctor> {
    throw new Error('Method not implemented')
  }

  async deleteDoctor(id: string): Promise<void> {
    throw new Error('Method not implemented')
  }

  async getDoctors(params?: PaginationParams): Promise<PaginatedResponse<Doctor>> {
    throw new Error('Method not implemented')
  }

  async createAppointment(appointmentData: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>): Promise<Appointment> {
    throw new Error('Method not implemented')
  }

  async getAppointmentById(id: string): Promise<Appointment | null> {
    throw new Error('Method not implemented')
  }

  async updateAppointment(id: string, appointmentData: Partial<Appointment>): Promise<Appointment> {
    throw new Error('Method not implemented')
  }

  async deleteAppointment(id: string): Promise<void> {
    throw new Error('Method not implemented')
  }

  async getAppointments(params?: PaginationParams): Promise<PaginatedResponse<Appointment>> {
    throw new Error('Method not implemented')
  }

  async getAppointmentsByPatient(patientId: string, params?: PaginationParams): Promise<PaginatedResponse<Appointment>> {
    throw new Error('Method not implemented')
  }

  async getAppointmentsByDoctor(doctorId: string, params?: PaginationParams): Promise<PaginatedResponse<Appointment>> {
    throw new Error('Method not implemented')
  }

  async createMedicalRecord(recordData: Omit<MedicalRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<MedicalRecord> {
    throw new Error('Method not implemented')
  }

  async getMedicalRecordById(id: string): Promise<MedicalRecord | null> {
    throw new Error('Method not implemented')
  }

  async updateMedicalRecord(id: string, recordData: Partial<MedicalRecord>): Promise<MedicalRecord> {
    throw new Error('Method not implemented')
  }

  async deleteMedicalRecord(id: string): Promise<void> {
    throw new Error('Method not implemented')
  }

  async getMedicalRecords(params?: PaginationParams): Promise<PaginatedResponse<MedicalRecord>> {
    throw new Error('Method not implemented')
  }

  async getMedicalRecordsByPatient(patientId: string, params?: PaginationParams): Promise<PaginatedResponse<MedicalRecord>> {
    throw new Error('Method not implemented')
  }

  // Doctor Notes operations
  async createDoctorNote(noteData: CreateDoctorNoteData): Promise<DoctorNote> {
    try {
      const [note] = await db.insert(doctorNotes).values({
        patientId: noteData.patientId,
        doctorId: noteData.doctorId,
        appointmentId: noteData.appointmentId,
        title: noteData.title,
        content: noteData.content,
        noteType: noteData.noteType || 'general',
        isPrivate: noteData.isPrivate || false,
        tags: noteData.tags ? JSON.stringify(noteData.tags) : null,
      }).returning()

      return {
        id: note.id,
        patientId: note.patientId,
        doctorId: note.doctorId,
        appointmentId: note.appointmentId || undefined,
        title: note.title,
        content: note.content,
        noteType: note.noteType as 'general' | 'consultation' | 'follow-up' | 'emergency',
        isPrivate: note.isPrivate || false,
        tags: note.tags ? JSON.parse(note.tags as string) : undefined,
        version: note.version || 1,
        isDeleted: note.isDeleted || false,
        deletedAt: note.deletedAt || undefined,
        createdAt: note.createdAt,
        updatedAt: note.updatedAt,
      }
    } catch (error) {
      console.error('Error creating doctor note:', error)
      throw new Error('Failed to create doctor note')
    }
  }

  async getDoctorNoteById(id: string): Promise<DoctorNote | null> {
    try {
      const [note] = await db
        .select()
        .from(doctorNotes)
        .where(and(eq(doctorNotes.id, id), eq(doctorNotes.isDeleted, false)))
        .limit(1)

      if (!note) return null

      return {
        id: note.id,
        patientId: note.patientId,
        doctorId: note.doctorId,
        appointmentId: note.appointmentId || undefined,
        title: note.title,
        content: note.content,
        noteType: note.noteType as 'general' | 'consultation' | 'follow-up' | 'emergency',
        isPrivate: note.isPrivate || false,
        tags: note.tags ? JSON.parse(note.tags as string) : undefined,
        version: note.version || 1,
        isDeleted: note.isDeleted || false,
        deletedAt: note.deletedAt || undefined,
        createdAt: note.createdAt,
        updatedAt: note.updatedAt,
      }
    } catch (error) {
      console.error('Error getting doctor note:', error)
      throw new Error('Failed to get doctor note')
    }
  }

  async updateDoctorNote(id: string, noteData: UpdateDoctorNoteData): Promise<DoctorNote> {
    try {
      const updateData: any = {}

      if (noteData.title !== undefined) updateData.title = noteData.title
      if (noteData.content !== undefined) updateData.content = noteData.content
      if (noteData.noteType !== undefined) updateData.noteType = noteData.noteType
      if (noteData.isPrivate !== undefined) updateData.isPrivate = noteData.isPrivate
      if (noteData.tags !== undefined) updateData.tags = JSON.stringify(noteData.tags)

      updateData.updatedAt = new Date()

      const [note] = await db
        .update(doctorNotes)
        .set(updateData)
        .where(and(eq(doctorNotes.id, id), eq(doctorNotes.isDeleted, false)))
        .returning()

      if (!note) {
        throw new Error('Doctor note not found')
      }

      return {
        id: note.id,
        patientId: note.patientId,
        doctorId: note.doctorId,
        appointmentId: note.appointmentId || undefined,
        title: note.title,
        content: note.content,
        noteType: note.noteType as 'general' | 'consultation' | 'follow-up' | 'emergency',
        isPrivate: note.isPrivate || false,
        tags: note.tags ? JSON.parse(note.tags as string) : undefined,
        version: note.version || 1,
        isDeleted: note.isDeleted || false,
        deletedAt: note.deletedAt || undefined,
        createdAt: note.createdAt,
        updatedAt: note.updatedAt,
      }
    } catch (error) {
      console.error('Error updating doctor note:', error)
      throw new Error('Failed to update doctor note')
    }
  }

  async deleteDoctorNote(id: string): Promise<void> {
    try {
      await db
        .update(doctorNotes)
        .set({
          isDeleted: true,
          deletedAt: new Date(),
          updatedAt: new Date()
        })
        .where(eq(doctorNotes.id, id))
    } catch (error) {
      console.error('Error deleting doctor note:', error)
      throw new Error('Failed to delete doctor note')
    }
  }

  async getDoctorNotes(filters?: DoctorNoteFilters, params?: PaginationParams): Promise<PaginatedResponse<DoctorNote>> {
    try {
      const page = params?.page || 1
      const limit = params?.limit || 10
      const offset = (page - 1) * limit

      let whereConditions = [eq(doctorNotes.isDeleted, false)]

      if (filters?.patientId) {
        whereConditions.push(eq(doctorNotes.patientId, filters.patientId))
      }
      if (filters?.doctorId) {
        whereConditions.push(eq(doctorNotes.doctorId, filters.doctorId))
      }
      if (filters?.noteType) {
        whereConditions.push(eq(doctorNotes.noteType, filters.noteType))
      }
      if (filters?.isPrivate !== undefined) {
        whereConditions.push(eq(doctorNotes.isPrivate, filters.isPrivate))
      }
      if (filters?.dateFrom) {
        whereConditions.push(gte(doctorNotes.createdAt, filters.dateFrom))
      }
      if (filters?.dateTo) {
        whereConditions.push(lte(doctorNotes.createdAt, filters.dateTo))
      }
      if (filters?.searchTerm) {
        whereConditions.push(
          or(
            ilike(doctorNotes.title, `%${filters.searchTerm}%`),
            ilike(doctorNotes.content, `%${filters.searchTerm}%`)
          )!
        )
      }

      const [notes, totalResult] = await Promise.all([
        db
          .select()
          .from(doctorNotes)
          .where(and(...whereConditions))
          .orderBy(desc(doctorNotes.createdAt))
          .limit(limit)
          .offset(offset),
        db
          .select({ count: count() })
          .from(doctorNotes)
          .where(and(...whereConditions))
      ])

      const total = totalResult[0]?.count || 0
      const totalPages = Math.ceil(total / limit)

      return {
        data: notes.map(note => ({
          id: note.id,
          patientId: note.patientId,
          doctorId: note.doctorId,
          appointmentId: note.appointmentId || undefined,
          title: note.title,
          content: note.content,
          noteType: note.noteType as 'general' | 'consultation' | 'follow-up' | 'emergency',
          isPrivate: note.isPrivate || false,
          tags: note.tags ? JSON.parse(note.tags as string) : undefined,
          version: note.version || 1,
          isDeleted: note.isDeleted || false,
          deletedAt: note.deletedAt || undefined,
          createdAt: note.createdAt,
          updatedAt: note.updatedAt,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        }
      }
    } catch (error) {
      console.error('Error getting doctor notes:', error)
      throw new Error('Failed to get doctor notes')
    }
  }

  async getDoctorNotesByPatient(patientId: string, doctorId?: string, params?: PaginationParams): Promise<PaginatedResponse<DoctorNote>> {
    const filters: DoctorNoteFilters = { patientId }
    if (doctorId) filters.doctorId = doctorId
    return this.getDoctorNotes(filters, params)
  }

  async getDoctorNotesByDoctor(doctorId: string, params?: PaginationParams): Promise<PaginatedResponse<DoctorNote>> {
    const filters: DoctorNoteFilters = { doctorId }
    return this.getDoctorNotes(filters, params)
  }

  async searchDoctorNotes(searchTerm: string, doctorId?: string, params?: PaginationParams): Promise<PaginatedResponse<DoctorNote>> {
    const filters: DoctorNoteFilters = { searchTerm }
    if (doctorId) filters.doctorId = doctorId
    return this.getDoctorNotes(filters, params)
  }

  // Patient-Doctor Assignment operations
  async createPatientDoctorAssignment(assignmentData: CreatePatientDoctorAssignmentData): Promise<PatientDoctorAssignment> {
    try {
      const [assignment] = await db.insert(patientDoctorAssignments).values({
        patientId: assignmentData.patientId,
        doctorId: assignmentData.doctorId,
        assignedBy: assignmentData.assignedBy,
        isPrimary: assignmentData.isPrimary || false,
        notes: assignmentData.notes,
      }).returning()

      return {
        id: assignment.id,
        patientId: assignment.patientId,
        doctorId: assignment.doctorId,
        assignedBy: assignment.assignedBy,
        isPrimary: assignment.isPrimary || false,
        assignmentDate: assignment.assignmentDate,
        isActive: assignment.isActive || true,
        notes: assignment.notes || undefined,
        createdAt: assignment.createdAt,
        updatedAt: assignment.updatedAt,
      }
    } catch (error) {
      console.error('Error creating patient-doctor assignment:', error)
      throw new Error('Failed to create patient-doctor assignment')
    }
  }

  async getPatientDoctorAssignmentById(id: string): Promise<PatientDoctorAssignment | null> {
    try {
      const [assignment] = await db
        .select()
        .from(patientDoctorAssignments)
        .where(eq(patientDoctorAssignments.id, id))
        .limit(1)

      if (!assignment) return null

      return {
        id: assignment.id,
        patientId: assignment.patientId,
        doctorId: assignment.doctorId,
        assignedBy: assignment.assignedBy,
        isPrimary: assignment.isPrimary || false,
        assignmentDate: assignment.assignmentDate,
        isActive: assignment.isActive || true,
        notes: assignment.notes || undefined,
        createdAt: assignment.createdAt,
        updatedAt: assignment.updatedAt,
      }
    } catch (error) {
      console.error('Error getting patient-doctor assignment:', error)
      throw new Error('Failed to get patient-doctor assignment')
    }
  }

  async updatePatientDoctorAssignment(id: string, assignmentData: UpdatePatientDoctorAssignmentData): Promise<PatientDoctorAssignment> {
    try {
      const updateData: any = { updatedAt: new Date() }

      if (assignmentData.isPrimary !== undefined) updateData.isPrimary = assignmentData.isPrimary
      if (assignmentData.isActive !== undefined) updateData.isActive = assignmentData.isActive
      if (assignmentData.notes !== undefined) updateData.notes = assignmentData.notes

      const [assignment] = await db
        .update(patientDoctorAssignments)
        .set(updateData)
        .where(eq(patientDoctorAssignments.id, id))
        .returning()

      if (!assignment) {
        throw new Error('Patient-doctor assignment not found')
      }

      return {
        id: assignment.id,
        patientId: assignment.patientId,
        doctorId: assignment.doctorId,
        assignedBy: assignment.assignedBy,
        isPrimary: assignment.isPrimary || false,
        assignmentDate: assignment.assignmentDate,
        isActive: assignment.isActive || true,
        notes: assignment.notes || undefined,
        createdAt: assignment.createdAt,
        updatedAt: assignment.updatedAt,
      }
    } catch (error) {
      console.error('Error updating patient-doctor assignment:', error)
      throw new Error('Failed to update patient-doctor assignment')
    }
  }

  async deletePatientDoctorAssignment(id: string): Promise<void> {
    try {
      await db
        .delete(patientDoctorAssignments)
        .where(eq(patientDoctorAssignments.id, id))
    } catch (error) {
      console.error('Error deleting patient-doctor assignment:', error)
      throw new Error('Failed to delete patient-doctor assignment')
    }
  }

  async getPatientDoctorAssignments(patientId?: string, doctorId?: string, params?: PaginationParams): Promise<PaginatedResponse<PatientDoctorAssignment>> {
    try {
      const page = params?.page || 1
      const limit = params?.limit || 10
      const offset = (page - 1) * limit

      let whereConditions = [eq(patientDoctorAssignments.isActive, true)]

      if (patientId) {
        whereConditions.push(eq(patientDoctorAssignments.patientId, patientId))
      }
      if (doctorId) {
        whereConditions.push(eq(patientDoctorAssignments.doctorId, doctorId))
      }

      const [assignments, totalResult] = await Promise.all([
        db
          .select()
          .from(patientDoctorAssignments)
          .where(and(...whereConditions))
          .orderBy(desc(patientDoctorAssignments.assignmentDate))
          .limit(limit)
          .offset(offset),
        db
          .select({ count: count() })
          .from(patientDoctorAssignments)
          .where(and(...whereConditions))
      ])

      const total = totalResult[0]?.count || 0
      const totalPages = Math.ceil(total / limit)

      return {
        data: assignments.map(assignment => ({
          id: assignment.id,
          patientId: assignment.patientId,
          doctorId: assignment.doctorId,
          assignedBy: assignment.assignedBy,
          isPrimary: assignment.isPrimary || false,
          assignmentDate: assignment.assignmentDate,
          isActive: assignment.isActive || true,
          notes: assignment.notes || undefined,
          createdAt: assignment.createdAt,
          updatedAt: assignment.updatedAt,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        }
      }
    } catch (error) {
      console.error('Error getting patient-doctor assignments:', error)
      throw new Error('Failed to get patient-doctor assignments')
    }
  }

  async getPatientsByDoctor(doctorId: string, params?: PaginationParams): Promise<PaginatedResponse<Patient>> {
    throw new Error('Method not implemented')
  }

  async getDoctorsByPatient(patientId: string, params?: PaginationParams): Promise<PaginatedResponse<Doctor>> {
    throw new Error('Method not implemented')
  }

  async assignPatientToDoctor(patientId: string, doctorId: string, assignedBy: string, isPrimary?: boolean, notes?: string): Promise<PatientDoctorAssignment> {
    return this.createPatientDoctorAssignment({
      patientId,
      doctorId,
      assignedBy,
      isPrimary,
      notes
    })
  }

  async unassignPatientFromDoctor(patientId: string, doctorId: string): Promise<void> {
    try {
      await db
        .update(patientDoctorAssignments)
        .set({ isActive: false, updatedAt: new Date() })
        .where(
          and(
            eq(patientDoctorAssignments.patientId, patientId),
            eq(patientDoctorAssignments.doctorId, doctorId)
          )
        )
    } catch (error) {
      console.error('Error unassigning patient from doctor:', error)
      throw new Error('Failed to unassign patient from doctor')
    }
  }
}
