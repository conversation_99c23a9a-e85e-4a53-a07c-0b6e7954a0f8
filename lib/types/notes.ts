export interface DoctorNote {
  id: string
  patientId: string
  doctorId: string
  appointmentId?: string
  title: string
  content: string
  noteType: 'general' | 'consultation' | 'follow-up' | 'emergency'
  isPrivate: boolean
  tags?: string[]
  version: number
  isDeleted: boolean
  deletedAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface PatientDoctorAssignment {
  id: string
  patientId: string
  doctorId: string
  assignedBy: string
  isPrimary: boolean
  assignmentDate: Date
  isActive: boolean
  notes?: string
  createdAt: Date
  updatedAt: Date
}

export interface CreateDoctorNoteData {
  patientId: string
  doctorId: string
  appointmentId?: string
  title: string
  content: string
  noteType?: 'general' | 'consultation' | 'follow-up' | 'emergency'
  isPrivate?: boolean
  tags?: string[]
}

export interface UpdateDoctorNoteData {
  title?: string
  content?: string
  noteType?: 'general' | 'consultation' | 'follow-up' | 'emergency'
  isPrivate?: boolean
  tags?: string[]
}

export interface DoctorNoteFilters {
  patientId?: string
  doctorId?: string
  noteType?: string
  isPrivate?: boolean
  dateFrom?: Date
  dateTo?: Date
  searchTerm?: string
  tags?: string[]
}

export interface CreatePatientDoctorAssignmentData {
  patientId: string
  doctorId: string
  assignedBy: string
  isPrimary?: boolean
  notes?: string
}

export interface UpdatePatientDoctorAssignmentData {
  isPrimary?: boolean
  isActive?: boolean
  notes?: string
}
