import { NextRequest } from 'next/server'
import { getDatabaseAdapter } from '../database'
import { User, UserRole } from '../types'

/**
 * Authentication result for API routes
 */
export interface AuthResult {
  success: boolean
  user?: User
  error?: string
  status?: number
}

/**
 * Extract Firebase UID from Authorization header
 * Expected format: "Bearer <firebase-token>" or just the UID for testing
 */
function extractFirebaseUid(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization')
  
  if (!authHeader) {
    return null
  }

  // For testing/development, allow direct UID
  if (!authHeader.startsWith('Bearer ')) {
    return authHeader
  }

  // In production, you would verify the Firebase JWT token here
  // For now, we'll extract the UID from the token (simplified)
  const token = authHeader.replace('Bearer ', '')
  
  // TODO: Implement proper Firebase JWT verification
  // This is a simplified version for development
  return token
}

/**
 * Authenticate user from request headers
 */
export async function authenticateUser(request: NextRequest): Promise<AuthResult> {
  try {
    const firebaseUid = extractFirebaseUid(request)
    
    if (!firebaseUid) {
      return {
        success: false,
        error: 'Authentication required',
        status: 401
      }
    }

    const dataAdapter = getDatabaseAdapter()
    const user = await dataAdapter.getUserByFirebaseUid(firebaseUid)

    if (!user) {
      return {
        success: false,
        error: 'User not found',
        status: 404
      }
    }

    return {
      success: true,
      user
    }
  } catch (error) {
    console.error('Authentication error:', error)
    return {
      success: false,
      error: 'Authentication failed',
      status: 500
    }
  }
}

/**
 * Check if user has required role(s)
 */
export function hasRequiredRole(user: User, allowedRoles: UserRole[]): boolean {
  return allowedRoles.includes(user.role)
}

/**
 * Check if user can access doctor features
 * Admins and doctors can access doctor features
 */
export function canAccessDoctorFeatures(user: User): boolean {
  return user.role === 'admin' || user.role === 'doctor'
}

/**
 * Authenticate and authorize user for API routes
 */
export async function authenticateAndAuthorize(
  request: NextRequest,
  allowedRoles: UserRole[]
): Promise<AuthResult> {
  const authResult = await authenticateUser(request)
  
  if (!authResult.success || !authResult.user) {
    return authResult
  }

  if (!hasRequiredRole(authResult.user, allowedRoles)) {
    return {
      success: false,
      error: 'Insufficient permissions',
      status: 403
    }
  }

  return authResult
}

/**
 * Middleware wrapper for API routes that require authentication
 */
export function withAuth(allowedRoles: UserRole[]) {
  return async (request: NextRequest): Promise<AuthResult> => {
    return authenticateAndAuthorize(request, allowedRoles)
  }
}

/**
 * Middleware wrapper for doctor features (admin + doctor access)
 */
export function withDoctorAuth() {
  return withAuth(['admin', 'doctor'])
}
