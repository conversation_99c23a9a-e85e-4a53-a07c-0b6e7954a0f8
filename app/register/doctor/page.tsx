'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Stethoscope, CheckCircle, AlertCircle } from 'lucide-react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Button,
  Input,
  Alert,
  AlertDescription,
  LoadingState,
  useToast
} from '../../../components/ui'
import { useAuth } from '../../../lib/hooks'
import { validateEmail, validatePassword } from '../../../lib/utils/validation'

interface DoctorRegistrationForm {
  email: string
  password: string
  confirmPassword: string
  displayName: string
}

interface FormErrors {
  email?: string
  password?: string
  confirmPassword?: string
  displayName?: string
}

interface InvitationData {
  isValid: boolean
  email?: string
  message?: string
  invitation?: {
    email: string
    expiresAt: string
  }
}

export default function DoctorRegistrationPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { register } = useAuth()
  const { addToast } = useToast()
  
  const [token, setToken] = useState<string | null>(null)
  const [invitationData, setInvitationData] = useState<InvitationData | null>(null)
  const [isValidatingToken, setIsValidatingToken] = useState(true)
  const [formData, setFormData] = useState<DoctorRegistrationForm>({
    email: '',
    password: '',
    confirmPassword: '',
    displayName: ''
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)

  // Validate invitation token on page load
  useEffect(() => {
    const invitationToken = searchParams.get('token')
    
    if (!invitationToken) {
      setIsValidatingToken(false)
      setInvitationData({
        isValid: false,
        message: 'No invitation token provided'
      })
      return
    }

    setToken(invitationToken)
    validateInvitationToken(invitationToken)
  }, [searchParams])

  const validateInvitationToken = async (token: string) => {
    try {
      const response = await fetch(`/api/invitations/validate?token=${token}`)
      const data = await response.json()
      
      setInvitationData(data)
      
      if (data.isValid && data.invitation?.email) {
        setFormData(prev => ({
          ...prev,
          email: data.invitation.email
        }))
      }
    } catch (error) {
      setInvitationData({
        isValid: false,
        message: 'Failed to validate invitation token'
      })
    } finally {
      setIsValidatingToken(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear error for this field when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required'
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    } else if (invitationData?.invitation?.email && formData.email !== invitationData.invitation.email) {
      newErrors.email = 'Email must match the invitation'
    }

    // Display name validation
    if (!formData.displayName) {
      newErrors.displayName = 'Display name is required'
    } else if (formData.displayName.length < 2) {
      newErrors.displayName = 'Display name must be at least 2 characters'
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else {
      const passwordValidation = validatePassword(formData.password)
      if (!passwordValidation.isValid) {
        newErrors.password = passwordValidation.errors[0]
      }
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm() || !token) {
      return
    }

    setIsSubmitting(true)
    setSubmitError(null)

    try {
      await register({
        email: formData.email,
        password: formData.password,
        displayName: formData.displayName,
        role: 'doctor'
      }, token)

      // Show success toast
      addToast({
        title: 'Registration Successful!',
        description: 'Welcome to the Patient Management System',
      })

      // Redirect to doctor dashboard on success
      router.push('/dashboard/doctor')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed'
      setSubmitError(errorMessage)

      // Show error toast
      addToast({
        title: 'Registration Failed',
        description: errorMessage,
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Loading state while validating token
  if (isValidatingToken) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <LoadingState message="Validating invitation..." />
      </div>
    )
  }

  // Invalid token state
  if (!invitationData?.isValid) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto flex items-center justify-center w-12 h-12 rounded-full bg-red-100 mb-4">
              <AlertCircle className="w-6 h-6 text-red-600" />
            </div>
            <CardTitle className="text-xl font-bold text-red-900">
              Invalid Invitation
            </CardTitle>
            <CardDescription>
              {invitationData?.message || 'This invitation link is invalid or has expired.'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => router.push('/login')} 
              className="w-full"
              variant="outline"
            >
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 mb-4">
            <Stethoscope className="w-6 h-6 text-blue-600" />
          </div>
          <CardTitle className="text-2xl font-bold">
            Complete Your Registration
          </CardTitle>
          <CardDescription>
            You've been invited to join as a doctor. Please complete your registration below.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {submitError && (
              <Alert variant="destructive">
                <AlertDescription>{submitError}</AlertDescription>
              </Alert>
            )}

            <Input
              label="Email Address"
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              error={errors.email}
              placeholder="<EMAIL>"
              disabled={isSubmitting || !!invitationData?.invitation?.email}
              required
            />

            <Input
              label="Display Name"
              name="displayName"
              value={formData.displayName}
              onChange={handleInputChange}
              error={errors.displayName}
              placeholder="Dr. John Smith"
              disabled={isSubmitting}
              required
            />

            <Input
              label="Password"
              type="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              error={errors.password}
              placeholder="Create a secure password"
              disabled={isSubmitting}
              required
            />

            <Input
              label="Confirm Password"
              type="password"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              error={errors.confirmPassword}
              placeholder="Confirm your password"
              disabled={isSubmitting}
              required
            />

            <Button
              type="submit"
              className="w-full"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Creating Account...' : 'Complete Registration'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
