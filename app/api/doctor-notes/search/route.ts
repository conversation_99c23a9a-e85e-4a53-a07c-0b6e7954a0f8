import { NextRequest, NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../../lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const searchTerm = searchParams.get('q')
    const doctorId = searchParams.get('doctorId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    if (!searchTerm) {
      return NextResponse.json(
        { error: 'Search term is required' },
        { status: 400 }
      )
    }

    if (!doctorId) {
      return NextResponse.json(
        { error: 'Doctor ID is required' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()

    // Verify doctor exists
    const doctor = await dataAdapter.getDoctorById(doctorId)
    if (!doctor) {
      return NextResponse.json(
        { error: 'Doctor not found' },
        { status: 404 }
      )
    }

    const result = await dataAdapter.searchDoctorNotes(searchTerm, doctorId, { page, limit })

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
      searchTerm
    })
  } catch (error) {
    console.error('Error searching doctor notes:', error)
    return NextResponse.json(
      { error: 'Failed to search doctor notes' },
      { status: 500 }
    )
  }
}
