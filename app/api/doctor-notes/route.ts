import { NextRequest, NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../lib/database'
import { CreateDoctorNoteData, DoctorNoteFilters } from '../../../lib/types'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const doctorId = searchParams.get('doctorId')
    const patientId = searchParams.get('patientId')
    const noteType = searchParams.get('noteType')
    const isPrivate = searchParams.get('isPrivate')
    const searchTerm = searchParams.get('searchTerm')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    if (!doctorId) {
      return NextResponse.json(
        { error: 'Doctor ID is required' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()

    // Build filters
    const filters: DoctorNoteFilters = { doctorId }
    if (patientId) filters.patientId = patientId
    if (noteType) filters.noteType = noteType
    if (isPrivate !== null) filters.isPrivate = isPrivate === 'true'
    if (searchTerm) filters.searchTerm = searchTerm

    const result = await dataAdapter.getDoctorNotes(filters, { page, limit })

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination
    })
  } catch (error) {
    console.error('Error getting doctor notes:', error)
    return NextResponse.json(
      { error: 'Failed to get doctor notes' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { patientId, doctorId, title, content, noteType, isPrivate, tags, appointmentId } = body

    // Validate required fields
    if (!patientId || !doctorId || !title || !content) {
      return NextResponse.json(
        { error: 'Patient ID, Doctor ID, title, and content are required' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()

    // Verify doctor exists and has permission
    const doctor = await dataAdapter.getDoctorById(doctorId)
    if (!doctor) {
      return NextResponse.json(
        { error: 'Doctor not found' },
        { status: 404 }
      )
    }

    // Verify patient exists
    const patient = await dataAdapter.getPatientById(patientId)
    if (!patient) {
      return NextResponse.json(
        { error: 'Patient not found' },
        { status: 404 }
      )
    }

    // Create note data
    const noteData: CreateDoctorNoteData = {
      patientId,
      doctorId,
      title,
      content,
      noteType: noteType || 'general',
      isPrivate: isPrivate || false,
      tags: tags || [],
      appointmentId
    }

    const note = await dataAdapter.createDoctorNote(noteData)

    return NextResponse.json({
      success: true,
      message: 'Doctor note created successfully',
      data: note
    })
  } catch (error) {
    console.error('Error creating doctor note:', error)
    return NextResponse.json(
      { error: 'Failed to create doctor note' },
      { status: 500 }
    )
  }
}
