import { NextRequest, NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../../lib/database'
import { UpdateDoctorNoteData } from '../../../../lib/types'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    if (!id) {
      return NextResponse.json(
        { error: 'Note ID is required' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()
    const note = await dataAdapter.getDoctorNoteById(id)

    if (!note) {
      return NextResponse.json(
        { error: 'Doctor note not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: note
    })
  } catch (error) {
    console.error('Error getting doctor note:', error)
    return NextResponse.json(
      { error: 'Failed to get doctor note' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    const { title, content, noteType, isPrivate, tags, doctorId } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Note ID is required' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()

    // Verify note exists
    const existingNote = await dataAdapter.getDoctorNoteById(id)
    if (!existingNote) {
      return NextResponse.json(
        { error: 'Doctor note not found' },
        { status: 404 }
      )
    }

    // Verify doctor has permission to edit this note
    if (doctorId && existingNote.doctorId !== doctorId) {
      return NextResponse.json(
        { error: 'You can only edit your own notes' },
        { status: 403 }
      )
    }

    // Create update data
    const updateData: UpdateDoctorNoteData = {}
    if (title !== undefined) updateData.title = title
    if (content !== undefined) updateData.content = content
    if (noteType !== undefined) updateData.noteType = noteType
    if (isPrivate !== undefined) updateData.isPrivate = isPrivate
    if (tags !== undefined) updateData.tags = tags

    const updatedNote = await dataAdapter.updateDoctorNote(id, updateData)

    return NextResponse.json({
      success: true,
      message: 'Doctor note updated successfully',
      data: updatedNote
    })
  } catch (error) {
    console.error('Error updating doctor note:', error)
    return NextResponse.json(
      { error: 'Failed to update doctor note' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const { searchParams } = new URL(request.url)
    const doctorId = searchParams.get('doctorId')

    if (!id) {
      return NextResponse.json(
        { error: 'Note ID is required' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()

    // Verify note exists
    const existingNote = await dataAdapter.getDoctorNoteById(id)
    if (!existingNote) {
      return NextResponse.json(
        { error: 'Doctor note not found' },
        { status: 404 }
      )
    }

    // Verify doctor has permission to delete this note
    if (doctorId && existingNote.doctorId !== doctorId) {
      return NextResponse.json(
        { error: 'You can only delete your own notes' },
        { status: 403 }
      )
    }

    await dataAdapter.deleteDoctorNote(id)

    return NextResponse.json({
      success: true,
      message: 'Doctor note deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting doctor note:', error)
    return NextResponse.json(
      { error: 'Failed to delete doctor note' },
      { status: 500 }
    )
  }
}
