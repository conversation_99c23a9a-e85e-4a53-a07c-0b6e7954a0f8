import { NextRequest, NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../../lib/database'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    if (!id) {
      return NextResponse.json(
        { error: 'Patient ID is required' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()
    const patient = await dataAdapter.getPatientById(id)

    if (!patient) {
      return NextResponse.json(
        { error: 'Patient not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: patient
    })
  } catch (error) {
    console.error('Error getting patient:', error)
    return NextResponse.json(
      { error: 'Failed to get patient' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    const {
      firstName,
      lastName,
      email,
      phone,
      dateOfBirth,
      gender,
      address,
      emergencyContact,
      updatedBy
    } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Patient ID is required' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()

    // Verify patient exists
    const existingPatient = await dataAdapter.getPatientById(id)
    if (!existingPatient) {
      return NextResponse.json(
        { error: 'Patient not found' },
        { status: 404 }
      )
    }

    // Verify updater exists and has permission
    if (updatedBy) {
      const updater = await dataAdapter.getUserById(updatedBy)
      if (!updater) {
        return NextResponse.json(
          { error: 'Updater not found' },
          { status: 404 }
        )
      }

      // Only admins and doctors can update patients
      if (updater.role !== 'admin' && updater.role !== 'doctor') {
        return NextResponse.json(
          { error: 'Only administrators and doctors can update patients' },
          { status: 403 }
        )
      }
    }

    // Create update data
    const updateData: any = {}
    if (firstName !== undefined) updateData.firstName = firstName
    if (lastName !== undefined) updateData.lastName = lastName
    if (email !== undefined) {
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        return NextResponse.json(
          { error: 'Invalid email format' },
          { status: 400 }
        )
      }
      updateData.email = email
    }
    if (phone !== undefined) updateData.phone = phone
    if (dateOfBirth !== undefined) updateData.dateOfBirth = new Date(dateOfBirth)
    if (gender !== undefined) updateData.gender = gender
    if (address !== undefined) updateData.address = address
    if (emergencyContact !== undefined) updateData.emergencyContact = emergencyContact

    const updatedPatient = await dataAdapter.updatePatient(id, updateData)

    return NextResponse.json({
      success: true,
      message: 'Patient updated successfully',
      data: updatedPatient
    })
  } catch (error) {
    console.error('Error updating patient:', error)
    return NextResponse.json(
      { error: 'Failed to update patient' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const { searchParams } = new URL(request.url)
    const deletedBy = searchParams.get('deletedBy')

    if (!id) {
      return NextResponse.json(
        { error: 'Patient ID is required' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()

    // Verify patient exists
    const existingPatient = await dataAdapter.getPatientById(id)
    if (!existingPatient) {
      return NextResponse.json(
        { error: 'Patient not found' },
        { status: 404 }
      )
    }

    // Verify deleter exists and has permission
    if (deletedBy) {
      const deleter = await dataAdapter.getUserById(deletedBy)
      if (!deleter) {
        return NextResponse.json(
          { error: 'Deleter not found' },
          { status: 404 }
        )
      }

      // Only admins can delete patients
      if (deleter.role !== 'admin') {
        return NextResponse.json(
          { error: 'Only administrators can delete patients' },
          { status: 403 }
        )
      }
    }

    await dataAdapter.deletePatient(id)

    return NextResponse.json({
      success: true,
      message: 'Patient deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting patient:', error)
    return NextResponse.json(
      { error: 'Failed to delete patient' },
      { status: 500 }
    )
  }
}
