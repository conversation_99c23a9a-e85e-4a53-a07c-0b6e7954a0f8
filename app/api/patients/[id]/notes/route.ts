import { NextRequest, NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../../../lib/database'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: patientId } = params
    const { searchParams } = new URL(request.url)
    const doctorId = searchParams.get('doctorId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    if (!patientId) {
      return NextResponse.json(
        { error: 'Patient ID is required' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()

    // Verify patient exists
    const patient = await dataAdapter.getPatientById(patientId)
    if (!patient) {
      return NextResponse.json(
        { error: 'Patient not found' },
        { status: 404 }
      )
    }

    // If doctorId is provided, verify doctor exists and has access
    if (doctorId) {
      const doctor = await dataAdapter.getDoctorById(doctorId)
      if (!doctor) {
        return NextResponse.json(
          { error: 'Doctor not found' },
          { status: 404 }
        )
      }

      // Check if doctor is assigned to this patient
      const assignments = await dataAdapter.getPatientDoctorAssignments(patientId, doctorId)
      if (assignments.data.length === 0) {
        return NextResponse.json(
          { error: 'Doctor is not assigned to this patient' },
          { status: 403 }
        )
      }
    }

    const result = await dataAdapter.getDoctorNotesByPatient(patientId, doctorId, { page, limit })

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination
    })
  } catch (error) {
    console.error('Error getting patient notes:', error)
    return NextResponse.json(
      { error: 'Failed to get patient notes' },
      { status: 500 }
    )
  }
}
