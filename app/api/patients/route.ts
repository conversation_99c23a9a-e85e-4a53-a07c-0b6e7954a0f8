import { NextRequest, NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const doctorId = searchParams.get('doctorId')

    const dataAdapter = getDatabaseAdapter()

    let result
    if (doctorId) {
      // Get patients assigned to a specific doctor
      result = await dataAdapter.getPatientsByDoctor(doctorId, { page, limit })
    } else {
      // Get all patients (admin view)
      result = await dataAdapter.getPatients({ page, limit })
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination
    })
  } catch (error) {
    console.error('Error getting patients:', error)
    return NextResponse.json(
      { error: 'Failed to get patients' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      firstName,
      lastName,
      email,
      phone,
      dateOfBirth,
      gender,
      address,
      emergencyContact,
      medicalHistory,
      allergies,
      currentMedications,
      createdBy
    } = body

    // Validate required fields
    if (!firstName || !lastName || !email || !dateOfBirth) {
      return NextResponse.json(
        { error: 'First name, last name, email, and date of birth are required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()

    // Check if patient with this email already exists
    const existingPatients = await dataAdapter.getPatients({ page: 1, limit: 1 })
    // Note: This is a simplified check. In a real implementation, you'd want a more efficient method
    // to check for existing patients by email

    // Verify creator exists and has permission
    if (createdBy) {
      const creator = await dataAdapter.getUserById(createdBy)
      if (!creator) {
        return NextResponse.json(
          { error: 'Creator not found' },
          { status: 404 }
        )
      }

      // Only admins and doctors can create patients
      if (creator.role !== 'admin' && creator.role !== 'doctor') {
        return NextResponse.json(
          { error: 'Only administrators and doctors can create patients' },
          { status: 403 }
        )
      }
    }

    // Create patient data
    const patientData = {
      firstName,
      lastName,
      email,
      phone: phone || '',
      dateOfBirth: new Date(dateOfBirth),
      gender: gender || 'other',
      address: address || {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: ''
      },
      emergencyContact: emergencyContact || {
        name: '',
        relationship: '',
        phone: ''
      },
      medicalHistory: [],
      appointments: []
    }

    const patient = await dataAdapter.createPatient(patientData)

    return NextResponse.json({
      success: true,
      message: 'Patient created successfully',
      data: patient
    })
  } catch (error) {
    console.error('Error creating patient:', error)
    return NextResponse.json(
      { error: 'Failed to create patient' },
      { status: 500 }
    )
  }
}
