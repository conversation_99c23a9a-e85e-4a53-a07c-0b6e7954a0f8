import { NextRequest, NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../../../lib/database'

export async function GET(
  request: NextRequest,
  { params }: { params: { doctorId: string } }
) {
  try {
    const { doctorId } = params
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    if (!doctorId) {
      return NextResponse.json(
        { error: 'Doctor ID is required' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()

    // Verify doctor exists
    const doctor = await dataAdapter.getDoctorById(doctorId)
    if (!doctor) {
      return NextResponse.json(
        { error: 'Doctor not found' },
        { status: 404 }
      )
    }

    // Get patients assigned to this doctor
    const result = await dataAdapter.getPatientsByDoctor(doctorId, { page, limit })

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination
    })
  } catch (error) {
    console.error('Error getting patients for doctor:', error)
    return NextResponse.json(
      { error: 'Failed to get patients for doctor' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { doctorId: string } }
) {
  try {
    const { doctorId } = params
    const body = await request.json()
    const { patientId, assignedBy, isPrimary, notes } = body

    if (!doctorId || !patientId || !assignedBy) {
      return NextResponse.json(
        { error: 'Doctor ID, Patient ID, and Assigned By are required' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()

    // Verify doctor exists
    const doctor = await dataAdapter.getDoctorById(doctorId)
    if (!doctor) {
      return NextResponse.json(
        { error: 'Doctor not found' },
        { status: 404 }
      )
    }

    // Verify patient exists
    const patient = await dataAdapter.getPatientById(patientId)
    if (!patient) {
      return NextResponse.json(
        { error: 'Patient not found' },
        { status: 404 }
      )
    }

    // Verify assigner exists and has permission
    const assigner = await dataAdapter.getUserById(assignedBy)
    if (!assigner) {
      return NextResponse.json(
        { error: 'Assigner not found' },
        { status: 404 }
      )
    }

    // Only admins and doctors can assign patients
    if (assigner.role !== 'admin' && assigner.role !== 'doctor') {
      return NextResponse.json(
        { error: 'Only administrators and doctors can assign patients' },
        { status: 403 }
      )
    }

    const assignment = await dataAdapter.assignPatientToDoctor(
      patientId,
      doctorId,
      assignedBy,
      isPrimary,
      notes
    )

    return NextResponse.json({
      success: true,
      message: 'Patient assigned to doctor successfully',
      data: assignment
    })
  } catch (error) {
    console.error('Error assigning patient to doctor:', error)
    return NextResponse.json(
      { error: 'Failed to assign patient to doctor' },
      { status: 500 }
    )
  }
}
