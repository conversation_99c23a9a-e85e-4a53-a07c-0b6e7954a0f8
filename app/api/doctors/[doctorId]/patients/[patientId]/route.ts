import { NextRequest, NextResponse } from 'next/server'
import { getDatabaseAdapter } from '../../../../../../lib/database'

export async function DELETE(
  request: NextRequest,
  { params }: { params: { doctorId: string; patientId: string } }
) {
  try {
    const { doctorId, patientId } = params
    const { searchParams } = new URL(request.url)
    const requesterId = searchParams.get('requesterId')

    if (!doctorId || !patientId) {
      return NextResponse.json(
        { error: 'Doctor ID and Patient ID are required' },
        { status: 400 }
      )
    }

    if (!requesterId) {
      return NextResponse.json(
        { error: 'Requester ID is required' },
        { status: 400 }
      )
    }

    const dataAdapter = getDatabaseAdapter()

    // Verify requester exists and has permission
    const requester = await dataAdapter.getUserById(requesterId)
    if (!requester) {
      return NextResponse.json(
        { error: 'Requester not found' },
        { status: 404 }
      )
    }

    // Only admins and the doctor themselves can unassign patients
    if (requester.role !== 'admin') {
      const doctorUser = await dataAdapter.getDoctorByUserId(requesterId)
      if (!doctorUser || doctorUser.id !== doctorId) {
        return NextResponse.json(
          { error: 'You can only unassign patients from yourself' },
          { status: 403 }
        )
      }
    }

    // Verify doctor exists
    const doctor = await dataAdapter.getDoctorById(doctorId)
    if (!doctor) {
      return NextResponse.json(
        { error: 'Doctor not found' },
        { status: 404 }
      )
    }

    // Verify patient exists
    const patient = await dataAdapter.getPatientById(patientId)
    if (!patient) {
      return NextResponse.json(
        { error: 'Patient not found' },
        { status: 404 }
      )
    }

    await dataAdapter.unassignPatientFromDoctor(patientId, doctorId)

    return NextResponse.json({
      success: true,
      message: 'Patient unassigned from doctor successfully'
    })
  } catch (error) {
    console.error('Error unassigning patient from doctor:', error)
    return NextResponse.json(
      { error: 'Failed to unassign patient from doctor' },
      { status: 500 }
    )
  }
}
