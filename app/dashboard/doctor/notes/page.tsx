'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { ProtectedRoute } from '../../../../components/auth'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>oteForm, <PERSON><PERSON><PERSON>View } from '../../../../components/doctor'
import { DoctorN<PERSON> } from '../../../../lib/types'
import { Button } from '../../../../components/ui/button'
import { ArrowLeft } from 'lucide-react'

export default function DoctorNotesPage() {
  const router = useRouter()
  const [selectedNote, setSelectedNote] = useState<DoctorNote | null>(null)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [showEditForm, setShowEditForm] = useState(false)

  // Mock doctor ID - in a real app, this would come from authentication context
  const doctorId = 'mock-doctor-id'

  const handleNoteSelect = (note: DoctorNote) => {
    setSelectedNote(note)
    setShowCreateForm(false)
    setShowEditForm(false)
  }

  const handleCreateNote = () => {
    setSelectedNote(null)
    setShowCreateForm(true)
    setShowEditForm(false)
  }

  const handleEditNote = (note: DoctorNote) => {
    setSelectedNote(note)
    setShowEditForm(true)
    setShowCreateForm(false)
  }

  const handleFormSave = (note: DoctorNote) => {
    setSelectedNote(note)
    setShowCreateForm(false)
    setShowEditForm(false)
    // The notes list will refresh automatically
  }

  const handleFormCancel = () => {
    setShowCreateForm(false)
    setShowEditForm(false)
  }

  const handleNoteDelete = (noteId: string) => {
    if (selectedNote?.id === noteId) {
      setSelectedNote(null)
    }
    // The notes list will refresh automatically
  }

  const handleBackToNotes = () => {
    setSelectedNote(null)
    setShowCreateForm(false)
    setShowEditForm(false)
  }

  const handleBackToDashboard = () => {
    router.push('/dashboard/doctor')
  }

  // For creating a note without a specific patient, we'll need to select one
  const handleCreateNoteWithPatient = () => {
    // In a real implementation, you might show a patient selector modal
    // For now, we'll redirect to the patients page
    router.push('/dashboard/doctor/patients')
  }

  return (
    <ProtectedRoute allowedRoles={['doctor']}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBackToDashboard}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {showCreateForm ? 'Create New Note' :
                 showEditForm ? 'Edit Note' :
                 selectedNote ? 'Note Details' :
                 'My Notes'}
              </h1>
              <p className="text-muted-foreground">
                {showCreateForm ? 'Add a new medical note' :
                 showEditForm ? 'Update note information' :
                 selectedNote ? 'View and manage note details' :
                 'View and manage all your medical notes'}
              </p>
            </div>
          </div>
          
          {!showCreateForm && !showEditForm && !selectedNote && (
            <Button onClick={handleCreateNoteWithPatient}>
              Create New Note
            </Button>
          )}
        </div>

        {/* Content */}
        {showCreateForm ? (
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">
              To create a note, please select a patient first.
            </p>
            <Button onClick={() => router.push('/dashboard/doctor/patients')}>
              Go to Patients
            </Button>
          </div>
        ) : showEditForm && selectedNote ? (
          <DoctorNoteForm
            doctorId={doctorId}
            patientId={selectedNote.patientId}
            note={selectedNote}
            onSave={handleFormSave}
            onCancel={handleFormCancel}
          />
        ) : selectedNote ? (
          <div className="space-y-4">
            <Button variant="outline" onClick={handleBackToNotes}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Notes
            </Button>
            <DoctorNoteView
              note={selectedNote}
              doctorId={doctorId}
              onEdit={handleEditNote}
              onDelete={handleNoteDelete}
              onClose={handleBackToNotes}
            />
          </div>
        ) : (
          <DoctorNotesList
            doctorId={doctorId}
            onNoteSelect={handleNoteSelect}
            onCreateNote={handleCreateNoteWithPatient}
          />
        )}
      </div>
    </ProtectedRoute>
  )
}
