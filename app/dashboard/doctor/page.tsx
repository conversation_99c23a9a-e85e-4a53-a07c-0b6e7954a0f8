'use client'

import React, { useState, useEffect } from 'react'
import { Calendar, Users, FileText, Clock, Plus, Search, NotebookPen } from 'lucide-react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Button,
  Badge,
  Input
} from '../../../components/ui'
import { ProtectedRoute } from '../../../components/auth'
import { formatDate, formatTime } from '../../../lib/utils'
import { useRouter } from 'next/navigation'

// Mock data - in a real app, this would come from your data adapter
const mockStats = {
  totalPatients: 45,
  todayAppointments: 8,
  pendingRecords: 3,
  completedToday: 5,
}

const mockTodayAppointments = [
  {
    id: 1,
    patientName: '<PERSON>',
    time: '09:00 AM',
    type: 'consultation',
    status: 'confirmed',
    duration: 30,
  },
  {
    id: 2,
    patientName: '<PERSON>',
    time: '10:30 AM',
    type: 'follow-up',
    status: 'confirmed',
    duration: 15,
  },
  {
    id: 3,
    patientName: '<PERSON>',
    time: '02:00 PM',
    type: 'routine',
    status: 'pending',
    duration: 30,
  },
  {
    id: 4,
    patientName: '<PERSON>',
    time: '03:30 PM',
    type: 'emergency',
    status: 'confirmed',
    duration: 45,
  },
]

const mockRecentPatients = [
  {
    id: 1,
    name: 'John Doe',
    age: 45,
    lastVisit: '2024-01-15',
    condition: 'Hypertension',
    status: 'stable',
  },
  {
    id: 2,
    name: 'Jane Smith',
    age: 32,
    lastVisit: '2024-01-14',
    condition: 'Diabetes',
    status: 'monitoring',
  },
  {
    id: 3,
    name: 'Robert Johnson',
    age: 58,
    lastVisit: '2024-01-13',
    condition: 'Arthritis',
    status: 'stable',
  },
  {
    id: 4,
    name: 'Emily Davis',
    age: 28,
    lastVisit: '2024-01-12',
    condition: 'Migraine',
    status: 'improving',
  },
]

export default function DoctorDashboard() {
  const router = useRouter()
  const [stats, setStats] = useState(mockStats)
  const [todayAppointments, setTodayAppointments] = useState(mockTodayAppointments)
  const [recentPatients, setRecentPatients] = useState(mockRecentPatients)
  const [searchTerm, setSearchTerm] = useState('')

  // In a real app, you would fetch data here
  useEffect(() => {
    // fetchDoctorDashboardData()
  }, [])

  const StatCard = ({ 
    title, 
    value, 
    description, 
    icon: Icon,
    color = 'primary'
  }: {
    title: string
    value: number
    description: string
    icon: React.ComponentType<{ className?: string }>
    color?: string
  }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'default'
      case 'pending': return 'outline'
      case 'completed': return 'secondary'
      case 'cancelled': return 'destructive'
      default: return 'outline'
    }
  }

  const getConditionStatusColor = (status: string) => {
    switch (status) {
      case 'stable': return 'default'
      case 'improving': return 'secondary'
      case 'monitoring': return 'outline'
      case 'critical': return 'destructive'
      default: return 'outline'
    }
  }

  const filteredPatients = recentPatients.filter(patient =>
    patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.condition.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <ProtectedRoute allowedRoles={['admin', 'doctor']}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Doctor Dashboard</h1>
            <p className="text-muted-foreground">
              Manage your patients and appointments
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => router.push('/dashboard/doctor/patients')}>
              <Users className="h-4 w-4 mr-2" />
              My Patients
            </Button>
            <Button variant="outline" onClick={() => router.push('/dashboard/doctor/notes')}>
              <NotebookPen className="h-4 w-4 mr-2" />
              My Notes
            </Button>
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              View Schedule
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Appointment
            </Button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="My Patients"
            value={stats.totalPatients}
            description="Total assigned patients"
            icon={Users}
          />
          <StatCard
            title="Today's Appointments"
            value={stats.todayAppointments}
            description="Scheduled for today"
            icon={Calendar}
          />
          <StatCard
            title="Completed Today"
            value={stats.completedToday}
            description="Appointments completed"
            icon={Clock}
          />
          <StatCard
            title="Pending Records"
            value={stats.pendingRecords}
            description="Records to update"
            icon={FileText}
          />
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Today's Appointments */}
          <Card>
            <CardHeader>
              <CardTitle>Today's Appointments</CardTitle>
              <CardDescription>
                Your schedule for {formatDate(new Date())}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {todayAppointments.map((appointment) => (
                  <div key={appointment.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 rounded-full bg-primary flex items-center justify-center">
                        <span className="text-sm font-medium text-primary-foreground">
                          {appointment.patientName.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium">{appointment.patientName}</p>
                        <p className="text-xs text-muted-foreground">
                          {appointment.time} • {appointment.duration} min
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">
                        {appointment.type}
                      </Badge>
                      <Badge variant={getStatusColor(appointment.status)}>
                        {appointment.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
              <Button variant="outline" className="w-full mt-4">
                View Full Schedule
              </Button>
            </CardContent>
          </Card>

          {/* Recent Patients */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Patients</CardTitle>
              <CardDescription>
                Patients you've seen recently
              </CardDescription>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search patients..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredPatients.map((patient) => (
                  <div key={patient.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 rounded-full bg-secondary flex items-center justify-center">
                        <span className="text-sm font-medium">
                          {patient.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium">{patient.name}</p>
                        <p className="text-xs text-muted-foreground">
                          Age {patient.age} • Last visit: {formatDate(new Date(patient.lastVisit))}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {patient.condition}
                        </p>
                      </div>
                    </div>
                    <Badge variant={getConditionStatusColor(patient.status)}>
                      {patient.status}
                    </Badge>
                  </div>
                ))}
              </div>
              <Button
                variant="outline"
                className="w-full mt-4"
                onClick={() => router.push('/dashboard/doctor/patients')}
              >
                View All Patients
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <Button
                variant="outline"
                className="h-20 flex-col"
                onClick={() => router.push('/dashboard/doctor/patients')}
              >
                <Plus className="h-6 w-6 mb-2" />
                New Patient
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <Calendar className="h-6 w-6 mb-2" />
                Schedule Appointment
              </Button>
              <Button
                variant="outline"
                className="h-20 flex-col"
                onClick={() => router.push('/dashboard/doctor/notes')}
              >
                <NotebookPen className="h-6 w-6 mb-2" />
                My Notes
              </Button>
              <Button
                variant="outline"
                className="h-20 flex-col"
                onClick={() => router.push('/dashboard/doctor/patients')}
              >
                <Search className="h-6 w-6 mb-2" />
                Search Patients
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </ProtectedRoute>
  )
}
