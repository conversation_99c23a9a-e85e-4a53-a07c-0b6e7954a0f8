'use client'

import { useState } from 'react'
import { ProtectedRoute } from '../../../../components/auth'
import { PatientList, PatientForm } from '../../../../components/doctor'
import { Patient } from '../../../../lib/types'
import { Button } from '../../../../components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function DoctorPatientsPage() {
  const router = useRouter()
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [showEditForm, setShowEditForm] = useState(false)

  // Mock doctor ID - in a real app, this would come from authentication context
  const doctorId = 'mock-doctor-id'
  const userId = 'mock-user-id'

  const handlePatientSelect = (patient: Patient) => {
    setSelectedPatient(patient)
    setShowEditForm(false)
    setShowCreateForm(false)
  }

  const handleCreatePatient = () => {
    setSelectedPatient(null)
    setShowCreateForm(true)
    setShowEditForm(false)
  }

  const handleEditPatient = () => {
    if (selectedPatient) {
      setShowEditForm(true)
      setShowCreateForm(false)
    }
  }

  const handleFormSave = (patient: Patient) => {
    setSelectedPatient(patient)
    setShowCreateForm(false)
    setShowEditForm(false)
    // Refresh the patient list
    window.location.reload()
  }

  const handleFormCancel = () => {
    setShowCreateForm(false)
    setShowEditForm(false)
  }

  const handleBackToList = () => {
    setSelectedPatient(null)
    setShowCreateForm(false)
    setShowEditForm(false)
  }

  const handleViewNotes = () => {
    if (selectedPatient) {
      router.push(`/dashboard/doctor/patients/${selectedPatient.id}/notes`)
    }
  }

  return (
    <ProtectedRoute allowedRoles={['admin', 'doctor']}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/dashboard/doctor')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {showCreateForm ? 'Add New Patient' : 
                 showEditForm ? 'Edit Patient' :
                 selectedPatient ? `${selectedPatient.firstName} ${selectedPatient.lastName}` :
                 'My Patients'}
              </h1>
              <p className="text-muted-foreground">
                {showCreateForm ? 'Create a new patient record' :
                 showEditForm ? 'Update patient information' :
                 selectedPatient ? 'Patient details and management' :
                 'Manage your assigned patients'}
              </p>
            </div>
          </div>
          
          {selectedPatient && !showEditForm && !showCreateForm && (
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleEditPatient}>
                Edit Patient
              </Button>
              <Button onClick={handleViewNotes}>
                View Notes
              </Button>
            </div>
          )}
        </div>

        {/* Content */}
        {showCreateForm ? (
          <PatientForm
            createdBy={userId}
            onSave={handleFormSave}
            onCancel={handleFormCancel}
          />
        ) : showEditForm && selectedPatient ? (
          <PatientForm
            patient={selectedPatient}
            createdBy={userId}
            onSave={handleFormSave}
            onCancel={handleFormCancel}
          />
        ) : selectedPatient ? (
          <div className="space-y-6">
            <Button variant="outline" onClick={handleBackToList}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Patient List
            </Button>
            
            {/* Patient Details Card */}
            <div className="bg-white rounded-lg border p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Personal Information</h3>
                  <div className="space-y-2">
                    <div>
                      <span className="font-medium">Name:</span> {selectedPatient.firstName} {selectedPatient.lastName}
                    </div>
                    <div>
                      <span className="font-medium">Email:</span> {selectedPatient.email}
                    </div>
                    <div>
                      <span className="font-medium">Phone:</span> {selectedPatient.phone || 'Not provided'}
                    </div>
                    <div>
                      <span className="font-medium">Date of Birth:</span> {new Date(selectedPatient.dateOfBirth).toLocaleDateString()}
                    </div>
                    <div>
                      <span className="font-medium">Gender:</span> {selectedPatient.gender}
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-4">Contact Information</h3>
                  <div className="space-y-2">
                    {selectedPatient.address && (
                      <div>
                        <span className="font-medium">Address:</span>
                        <div className="text-sm text-gray-600">
                          {selectedPatient.address.street && <div>{selectedPatient.address.street}</div>}
                          {(selectedPatient.address.city || selectedPatient.address.state) && (
                            <div>
                              {selectedPatient.address.city}{selectedPatient.address.city && selectedPatient.address.state && ', '}{selectedPatient.address.state}
                            </div>
                          )}
                          {selectedPatient.address.zipCode && <div>{selectedPatient.address.zipCode}</div>}
                          {selectedPatient.address.country && <div>{selectedPatient.address.country}</div>}
                        </div>
                      </div>
                    )}
                    
                    {selectedPatient.emergencyContact?.name && (
                      <div>
                        <span className="font-medium">Emergency Contact:</span>
                        <div className="text-sm text-gray-600">
                          <div>{selectedPatient.emergencyContact.name}</div>
                          {selectedPatient.emergencyContact.relationship && (
                            <div>Relationship: {selectedPatient.emergencyContact.relationship}</div>
                          )}
                          {selectedPatient.emergencyContact.phone && (
                            <div>Phone: {selectedPatient.emergencyContact.phone}</div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="mt-6 pt-6 border-t">
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>Patient since: {new Date(selectedPatient.createdAt).toLocaleDateString()}</span>
                  <span>Last updated: {new Date(selectedPatient.updatedAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <PatientList
            doctorId={doctorId}
            onPatientSelect={handlePatientSelect}
            onCreatePatient={handleCreatePatient}
          />
        )}
      </div>
    </ProtectedRoute>
  )
}
