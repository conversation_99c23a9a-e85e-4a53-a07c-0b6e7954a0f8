'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { ProtectedRoute } from '../../../../../../components/auth'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>F<PERSON>, Doctor<PERSON><PERSON>View } from '../../../../../../components/doctor'
import { DoctorNote, Patient } from '../../../../../../lib/types'
import { Button } from '../../../../../../components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { toast } from 'sonner'

export default function PatientNotesPage() {
  const params = useParams()
  const router = useRouter()
  const patientId = params.patientId as string
  
  const [patient, setPatient] = useState<Patient | null>(null)
  const [selectedNote, setSelectedNote] = useState<DoctorNote | null>(null)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [showEditForm, setShowEditForm] = useState(false)
  const [loading, setLoading] = useState(true)

  // Mock doctor ID - in a real app, this would come from authentication context
  const doctorId = 'mock-doctor-id'

  useEffect(() => {
    fetchPatient()
  }, [patientId])

  const fetchPatient = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/patients/${patientId}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch patient')
      }

      setPatient(data.data)
    } catch (error) {
      console.error('Error fetching patient:', error)
      toast.error('Failed to load patient information')
    } finally {
      setLoading(false)
    }
  }

  const handleNoteSelect = (note: DoctorNote) => {
    setSelectedNote(note)
    setShowCreateForm(false)
    setShowEditForm(false)
  }

  const handleCreateNote = () => {
    setSelectedNote(null)
    setShowCreateForm(true)
    setShowEditForm(false)
  }

  const handleEditNote = (note: DoctorNote) => {
    setSelectedNote(note)
    setShowEditForm(true)
    setShowCreateForm(false)
  }

  const handleFormSave = (note: DoctorNote) => {
    setSelectedNote(note)
    setShowCreateForm(false)
    setShowEditForm(false)
    // The notes list will refresh automatically
  }

  const handleFormCancel = () => {
    setShowCreateForm(false)
    setShowEditForm(false)
  }

  const handleNoteDelete = (noteId: string) => {
    if (selectedNote?.id === noteId) {
      setSelectedNote(null)
    }
    // The notes list will refresh automatically
  }

  const handleBackToNotes = () => {
    setSelectedNote(null)
    setShowCreateForm(false)
    setShowEditForm(false)
  }

  const handleBackToPatients = () => {
    router.push('/dashboard/doctor/patients')
  }

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={['doctor']}>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </ProtectedRoute>
    )
  }

  if (!patient) {
    return (
      <ProtectedRoute allowedRoles={['doctor']}>
        <div className="text-center py-8">
          <p className="text-gray-500">Patient not found</p>
          <Button onClick={handleBackToPatients} className="mt-4">
            Back to Patients
          </Button>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute allowedRoles={['admin', 'doctor']}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBackToPatients}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Patients
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {showCreateForm ? 'Create New Note' :
                 showEditForm ? 'Edit Note' :
                 selectedNote ? 'Note Details' :
                 `Notes for ${patient.firstName} ${patient.lastName}`}
              </h1>
              <p className="text-muted-foreground">
                {showCreateForm ? 'Add a new note for this patient' :
                 showEditForm ? 'Update note information' :
                 selectedNote ? 'View and manage note details' :
                 'Manage medical notes and observations'}
              </p>
            </div>
          </div>
          
          {!showCreateForm && !showEditForm && !selectedNote && (
            <Button onClick={handleCreateNote}>
              Create New Note
            </Button>
          )}
        </div>

        {/* Patient Info Bar */}
        {!showCreateForm && !showEditForm && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-blue-900">
                  {patient.firstName} {patient.lastName}
                </h3>
                <p className="text-sm text-blue-700">
                  {patient.email} • Age {new Date().getFullYear() - new Date(patient.dateOfBirth).getFullYear()}
                </p>
              </div>
              <div className="text-sm text-blue-600">
                Patient ID: {patient.id}
              </div>
            </div>
          </div>
        )}

        {/* Content */}
        {showCreateForm ? (
          <DoctorNoteForm
            doctorId={doctorId}
            patientId={patientId}
            onSave={handleFormSave}
            onCancel={handleFormCancel}
          />
        ) : showEditForm && selectedNote ? (
          <DoctorNoteForm
            doctorId={doctorId}
            patientId={patientId}
            note={selectedNote}
            onSave={handleFormSave}
            onCancel={handleFormCancel}
          />
        ) : selectedNote ? (
          <div className="space-y-4">
            <Button variant="outline" onClick={handleBackToNotes}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Notes
            </Button>
            <DoctorNoteView
              note={selectedNote}
              doctorId={doctorId}
              onEdit={handleEditNote}
              onDelete={handleNoteDelete}
              onClose={handleBackToNotes}
            />
          </div>
        ) : (
          <DoctorNotesList
            doctorId={doctorId}
            patientId={patientId}
            onNoteSelect={handleNoteSelect}
            onCreateNote={handleCreateNote}
          />
        )}
      </div>
    </ProtectedRoute>
  )
}
